{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%run ./config/config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%run ./config/table_config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%run ./utils/logger"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define logging configuration to be used across all notebooks\n", "# These variables will be available to other notebooks that run this notebook\n", "# Use log_path from config.ipynb instead of hardcoding the log file path\n", "component_log_levels = {\n", "    \"data_loading\": \"DEBUG\",  # More detailed logging for data loading operations\n", "    \"transformation\": \"INFO\",  # Standard logging for transformations\n", "    \"data_writing\": \"INFO\"     # Standard logging for data writing operations\n", "}\n", "\n", "# Create a logger for this notebook using log_path from config\n", "startup_logger = create_logger(log_level=\"INFO\", component_log_levels=component_log_levels)\n", "startup_logger.info(\"Initializing notebook\")\n", "startup_logger.info(f\"Log path from config: {log_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "cce6766b-9aee-4c17-8df7-5b39b45b9db0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE SCHEMA IF NOT EXISTS sobeys_uc.bronze;\n", "CREATE SCHEMA IF NOT EXISTS sobeys_uc.silver;\n", "CREATE SCHEMA IF NOT EXISTS sobeys_uc.gold;"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "start_up", "widgets": {}}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 0}