{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "cce6766b-9aee-4c17-8df7-5b39b45b9db0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Invoice Line - Bronze to Silver Transformation\n", "\n", "This notebook processes invoice line data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../utils/silver_utils"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../utils/schema_utils"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa06b87d-8138-445a-b0d4-39d2819f695d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import (\n", "    input_file_name, current_timestamp, lit, col, to_timestamp, when, \n", "    upper, regexp_extract, datediff, expr, year, month, quarter, dayofmonth, abs\n", ")\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "\n", "# Use the logger configuration from startup with log_path from config\n", "invoice_line_silver_logger = create_logger(component_log_levels=component_log_levels)\n", "invoice_line_silver_logger.info(\"Initializing notebook\")\n", " \n", "# Extract frequently used config values into variables\n", "catalog = pipeline_config[\"catalog\"]\n", "target_date = pipeline_config[\"default_processing_date\"]\n", "bronze_schema = pipeline_config[\"schemas\"][\"bronze\"]\n", "silver_schema = pipeline_config[\"schemas\"][\"silver\"]\n", "bronze_path = pipeline_config[\"paths\"][\"bronze_path\"]\n", "silver_path = pipeline_config[\"paths\"][\"silver_path\"]\n", "silver_format = pipeline_config[\"file_formats\"][\"silver\"]\n", "delta_properties = pipeline_config[\"delta_properties\"]\n", "\n", "# Switch to catalog\n", "spark.sql(f\"USE CATALOG {catalog}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d3fbae4c-e504-40ad-bd0e-54035b2beaf1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Get invoice_line table configuration from table_config\n", "invoice_line_config = table_config[\"tables\"][\"invoice_line\"]\n", "invoice_line_silver_logger.info(f\"Loaded invoice_line configuration from table_config\")\n", "\n", "# Table metadata for invoice_line table\n", "table_metadata = {\n", "    \"primary_key\": invoice_line_config[\"silver\"][\"primary_key\"],\n", "    \"column_mapping\": invoice_line_config[\"silver\"][\"column_mapping_bronze_to_silver\"],\n", "    \"timestamp_columns\": [\"invoice_date\", \"created_timestamp\", \"modified_timestamp\"],\n", "    \"numeric_columns\": [\"quantity\", \"unit_price\", \"discount_amount\", \"tax_amount\", \"line_total_amount\"],\n", "    \"foreign_keys\": {\n", "        \"invoice_id\": {\"table\": \"invoice\", \"column\": \"invoice_id\"},\n", "        \"discount_id\": {\"table\": \"discount\", \"column\": \"discount_id\"},\n", "        \"item_id\": {\"table\": \"item\", \"column\": \"item_id\"},\n", "        \"tax_id\": {\"table\": \"tax\", \"column\": \"tax_id\"},\n", "        \"customer_id\": {\"table\": \"customer\", \"column\": \"customer_id\"}\n", "    }\n", "}\n", "\n", "# Log the column mapping for debugging\n", "invoice_line_silver_logger.info(f\"Column mapping: {table_metadata['column_mapping']}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "234247ab-033a-4431-81ad-209eb7e59a91", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to apply transformations based on table metadata\n", "@log_execution(invoice_line_silver_logger)\n", "def apply_transformations(df):\n", "    invoice_line_silver_logger.info(\"Applying transformations to invoice_line data\")\n", "    \n", "    # Apply column mapping from bronze to silver\n", "    column_mapping = table_metadata[\"column_mapping\"]\n", "    df = apply_column_mapping(df, column_mapping)\n", "    \n", "    # Get the schema for invoice_line table in silver layer\n", "    invoice_line_schema = get_schema(\"invoice_line\", \"silver\")\n", "    \n", "    # Apply schema to enforce data types\n", "    if invoice_line_schema:\n", "        invoice_line_silver_logger.info(\"Applying explicit schema for data type enforcement\")\n", "        df = apply_schema(df, invoice_line_schema)\n", "    else:\n", "        # Fall back to individual conversions if schema is not available\n", "        invoice_line_silver_logger.info(\"Falling back to individual data type conversions\")\n", "        # Apply timestamp conversions\n", "        df = convert_timestamp_columns(df, table_metadata[\"timestamp_columns\"], \"M/d/yyyy\")\n", "        \n", "        # Apply numeric conversions\n", "        df = convert_numeric_columns(df, table_metadata[\"numeric_columns\"])\n", "    \n", "    # Validate quantity > 0\n", "    if \"quantity\" in df.columns:\n", "        df = df.withColumn(\"quantity_valid\", col(\"quantity\") > 0)\n", "    \n", "    # Validate unit_price >= 0\n", "    if \"unit_price\" in df.columns:\n", "        df = df.withColumn(\"unit_price_valid\", col(\"unit_price\") >= 0)\n", "    \n", "    # Calculate net_amount\n", "    if \"quantity\" in df.columns and \"unit_price\" in df.columns:\n", "        df = df.withColumn(\"net_amount\", col(\"quantity\") * col(\"unit_price\"))\n", "    \n", "    # Verify line_total_amount calculation\n", "    if all(col in df.columns for col in [\"quantity\", \"unit_price\", \"discount_amount\", \"tax_amount\", \"line_total_amount\"]):\n", "        df = df.withColumn(\n", "            \"calculated_total\", \n", "            (col(\"quantity\") * col(\"unit_price\")) - col(\"discount_amount\") + col(\"tax_amount\")\n", "        )\n", "        df = df.withColumn(\n", "            \"total_amount_valid\", \n", "            abs(col(\"calculated_total\") - col(\"line_total_amount\")) < 0.01\n", "        )\n", "    \n", "    # Extract date components for analytics\n", "    if \"invoice_date\" in df.columns:\n", "        df = df.withColumn(\"invoice_year\", year(col(\"invoice_date\")))\n", "        df = df.withColumn(\"invoice_month\", month(col(\"invoice_date\")))\n", "        df = df.withColumn(\"invoice_quarter\", quarter(col(\"invoice_date\")))\n", "        df = df.withColumn(\"invoice_day\", dayofmonth(col(\"invoice_date\")))\n", "    \n", "    # Check referential integrity for foreign keys\n", "    for fk_column, reference in table_metadata[\"foreign_keys\"].items():\n", "        if fk_column in df.columns:\n", "            try:\n", "                # Check if referenced table exists\n", "                ref_table = f\"{catalog}.{silver_schema}.{reference['table']}\"\n", "                try:\n", "                    spark.sql(f\"DESCRIBE TABLE {ref_table}\")\n", "                    table_exists = True\n", "                except AnalysisException:\n", "                    table_exists = False\n", "                \n", "                if table_exists:\n", "                    # Get distinct values from referenced table\n", "                    ref_values = spark.table(ref_table).select(reference['column']).distinct()\n", "                    \n", "                    # Add validation column\n", "                    validation_column = f\"{fk_column}_valid\"\n", "                    df = df.join(\n", "                        ref_values,\n", "                        df[fk_column] == ref_values[reference['column']],\n", "                        \"left\"\n", "                    ).withColumn(\n", "                        validation_column,\n", "                        col(reference['column']).isNotNull()\n", "                    ).drop(reference['column'])\n", "                else:\n", "                    # If referenced table doesn't exist yet, skip validation\n", "                    invoice_line_silver_logger.warning(f\"Referenced table {ref_table} does not exist. Skipping validation for {fk_column}.\")\n", "                    df = df.withColumn(f\"{fk_column}_valid\", lit(True))\n", "            except Exception as e:\n", "                invoice_line_silver_logger.error(f\"Error checking referential integrity for {fk_column}: {str(e)}\")\n", "                df = df.withColumn(f\"{fk_column}_valid\", lit(True))  # Default to valid in case of error\n", "    \n", "    # Validate primary key\n", "    df, pk_valid = validate_primary_key(df, table_metadata[\"primary_key\"])\n", "    if not pk_valid:\n", "        invoice_line_silver_logger.warning(f\"Primary key validation failed for {table_metadata['primary_key']}\")\n", "    \n", "    # Calculate data quality score\n", "    df = calculate_data_quality_score(df)\n", "    \n", "    # Add processing timestamp\n", "    df = df.withColumn(\"processed_at\", current_timestamp())\n", "    \n", "    return log_dataframe_info(df, \"invoice_line_transformed\", invoice_line_silver_logger)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Main processing function\n", "@log_execution(invoice_line_silver_logger)\n", "def process_bronze_to_silver(target_date=None):\n", "    invoice_line_silver_logger.info(f\"Processing invoice_line data from bronze to silver for date: {target_date if target_date else 'all'}\")\n", "    \n", "    # Get the schema for invoice_line table in silver layer\n", "    invoice_line_schema = get_schema(\"invoice_line\", \"silver\")\n", "    invoice_line_silver_logger.info(f\"Using explicit schema for invoice_line table: {invoice_line_schema}\")\n", "    \n", "    # Read bronze data directly using the utility function\n", "    bronze_df = read_bronze_data(\n", "        spark=spark,\n", "        bronze_path=bronze_path,\n", "        table_name=\"invoice_line\",\n", "        date=target_date,\n", "        logger=invoice_line_silver_logger\n", "    )\n", "    \n", "    if bronze_df is None or bronze_df.isEmpty():\n", "        invoice_line_silver_logger.warning(f\"No bronze data found for invoice_line on date {target_date}\")\n", "        return None\n", "    \n", "    # Apply transformations\n", "    silver_df = apply_transformations(bronze_df)\n", "    \n", "    # Write to silver layer directly using the utility function\n", "    final_df = write_silver_data(\n", "        spark=spark,\n", "        df=silver_df,\n", "        silver_path=silver_path,\n", "        table_name=\"invoice_line_cleaned\",\n", "        primary_key=table_metadata[\"primary_key\"],\n", "        column_mapping=None,  # Don't apply column mapping again as it's already applied in apply_transformations\n", "        catalog=catalog,\n", "        schema=silver_schema,\n", "        format=silver_format,\n", "        logger=invoice_line_silver_logger\n", "    )\n", "    \n", "    invoice_line_silver_logger.info(\"Completed bronze to silver processing for invoice_line_cleaned\")\n", "    return final_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Process invoice_line data\n", "silver_df = process_bronze_to_silver(target_date)\n", "if silver_df is not None:\n", "    log_dataframe_info(silver_df, \"invoice_line_silver_final\", invoice_line_silver_logger)\n", "    display(silver_df)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": null, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5135065695670842, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "invoice_line_silver", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}