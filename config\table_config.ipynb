# Table configuration dictionary
table_config = {
    "dim_date": {
        "schema": [
            {"name": "date_id", "type": "INT", "nullable": False},
            {"name": "day_name", "type": "STRING"},
            {"name": "month_name", "type": "STRING"},
            {"name": "day_of_week", "type": "INT"},
            {"name": "week_of_year", "type": "INT"},
            {"name": "month_number", "type": "INT"},
            {"name": "quarter", "type": "STRING"},
            {"name": "calendar_year", "type": "INT"},
            {"name": "fiscal_year", "type": "INT"},
            {"name": "is_weekend", "type": "BOOLEAN"},
            {"name": "full_date", "type": "DATE"},
        ],
        "primary_key": "date_id",
        "partition_by": "calendar_year",
    },
    "fact_invoice_line": {
        "schema": [
            {"name": "invoice_line_id", "type": "STRING", "nullable": False},
            {"name": "invoice_id", "type": "STRING", "nullable": False},
            {"name": "discount_id", "type": "STRING"},
            {"name": "item_id", "type": "STRING", "nullable": False},
            {"name": "tax_id", "type": "STRING"},
            {"name": "customer_id", "type": "STRING"},
            {"name": "site_id", "type": "STRING"},
            {"name": "invoice_date", "type": "DATE", "nullable": False},
            {
                "name": "quantity",
                "type": "INT",
                "nullable": False,
                "range_check": "> 0",
            },
            {"name": "unit_price", "type": "DECIMAL(10,2)", "range_check": ">= 0"},
            {"name": "discount_amount", "type": "DECIMAL(10,2)", "range_check": ">= 0"},
            {"name": "tax_amount", "type": "DECIMAL(10,2)", "range_check": ">= 0"},
            {
                "name": "line_total_amount",
                "type": "DECIMAL(10,2)",
                "range_check": ">= 0",
                "unique": True,
            },
        ],
        "primary_key": ["invoice_line_id", "invoice_id"],
        "foreign_keys": [
            {
                "column": "discount_id",
                "references": {"table": "dim_discount", "column": "discount_id"},
            },
            {
                "column": "item_id",
                "references": {"table": "dim_item", "column": "item_id"},
            },
            {
                "column": "tax_id",
                "references": {"table": "dim_tax", "column": "tax_id"},
            },
            {
                "column": "customer_id",
                "references": {"table": "dim_customer", "column": "customer_id"},
            },
            {
                "column": "site_id",
                "references": {"table": "dim_site", "column": "site_id"},
            },
            {
                "column": "invoice_date",
                "references": {"table": "dim_date", "column": "date_id"},
            },
        ],
        "column_mapping_bronze_to_silver": {
            "line_id": "invoice_line_id",
            "invoice_id": "invoice_id",
            "discount_code": "discount_id",
            "item_code": "item_id",
            "tax_code": "tax_id",
            "cust_identifier": "customer_id",
            "site_code": "site_id",
            "invoice_date": "invoice_date",
            "qty": "quantity",
            "price": "unit_price",
            "discount_value": "discount_amount",
            "tax_value": "tax_amount",
            "total_value": "line_total_amount",
        },
    },
    "dim_customer": {
        "schema": [
            {
                "name": "customer_id",
                "type": "STRING",
                "nullable": False,
                "primary_key": True,
            },
            {"name": "customer_name", "type": "STRING", "nullable": False},
            {"name": "customer_type", "type": "STRING", "nullable": False},
            {"name": "customer_email_address", "type": "STRING"},
            {"name": "phone_number", "type": "STRING"},
            {"name": "address", "type": "STRING"},
            {"name": "registration_date", "type": "DATE"},
            {"name": "is_active", "type": "BOOLEAN"},
            {"name": "loyalty_card_id", "type": "STRING"},
        ],
        "primary_key": "customer_id",
        "column_mapping_bronze_to_silver": {
            "cust_identifier": "customer_id",
            "name": "customer_name",
            "category": "customer_type",
            "mail": "customer_email_address",
            "telephone": "phone_number",
            "location": "address",
            "enroll_date": "registration_date",
            "status": "is_active",
            "membership_num": "loyalty_card_id",
        },
    },
    "dim_invoice": {
        "schema": [
            {"name": "invoice_id", "type": "STRING", "nullable": False, "unique": True},
            {"name": "invoice_type", "type": "STRING"},
            {"name": "status", "type": "STRING"},
            {"name": "reference_number", "type": "STRING"},
            {"name": "employee_id", "type": "INT"},
            {"name": "payment_method", "type": "STRING"},
            {"name": "channel_type", "type": "STRING"},
        ],
        "primary_key": "invoice_id",
        "column_mapping_bronze_to_silver": {
            "invoice_code": "invoice_id",
            "invoice_type": "invoice_type",
            "status": "status",
            "ref_num": "reference_number",
            "emp_id": "employee_id",
            "method": "payment_method",
            "channel_type": "channel_type",
        },
    },
    "dim_item": {
        "schema": [
            {"name": "item_id", "type": "STRING", "nullable": False, "unique": True},
            {"name": "product_id", "type": "STRING", "nullable": False},
            {"name": "item_name", "type": "STRING", "nullable": False},
            {"name": "unit_of_measure", "type": "STRING"},
            {"name": "package_size", "type": "STRING"},
            {"name": "measurement", "type": "STRING"},
            {"name": "nutritional_info", "type": "STRING"},
            {"name": "manufacture_date", "type": "DATE"},
            {"name": "expiry_date", "type": "DATE"},
            {"name": "is_active", "type": "BOOLEAN"},
        ],
        "primary_key": "item_id",
        "foreign_keys": [
            {
                "column": "product_id",
                "references": {"table": "dim_product", "column": "product_id"},
            }
        ],
        "column_mapping_bronze_to_silver": {
            "item_code": "item_id",
            "product_code": "product_id",
            "name": "item_name",
            "uom": "unit_of_measure",
            "pack_size": "package_size",
            "measure": "measurement",
            "info": "nutritional_info",
            "mfd": "manufacture_date",
            "exp": "expiry_date",
            "status": "is_active",
        },
    },
    "dim_product": {
        "schema": [
            {"name": "product_id", "type": "STRING", "nullable": False, "unique": True},
            {"name": "product_name", "type": "STRING", "nullable": False},
            {"name": "product_category", "type": "STRING", "nullable": False},
            {"name": "product_sub_category", "type": "STRING"},
            {"name": "product_description", "type": "STRING"},
            {"name": "brand_name", "type": "STRING"},
            {"name": "allergen_information", "type": "STRING"},
            {"name": "shelf_life_days", "type": "INT", "range_check": ">= 0"},
            {"name": "is_private_label", "type": "BOOLEAN"},
            {"name": "is_organic", "type": "BOOLEAN"},
            {"name": "is_perishable", "type": "BOOLEAN"},
            {"name": "is_active", "type": "BOOLEAN"},
        ],
        "primary_key": "product_id",
        "column_mapping_bronze_to_silver": {
            "product_code": "product_id",
            "name": "product_name",
            "category": "product_category",
            "sub_category": "product_sub_category",
            "desc": "product_description",
            "brand": "brand_name",
            "allergen_info": "allergen_information",
            "shelf_life": "shelf_life_days",
            "private_label": "is_private_label",
            "organic": "is_organic",
            "perishable": "is_perishable",
            "status": "is_active",
        },
    },
    "dim_cost": {
        "schema": [
            {"name": "cost_id", "type": "STRING", "nullable": False, "unique": True},
            {"name": "item_id", "type": "STRING", "nullable": False},
            {"name": "cost_type", "type": "STRING"},
            {
                "name": "selling_price",
                "type": "DECIMAL(10,2)",
                "nullable": False,
                "range_check": ">= 0",
            },
            {
                "name": "purchase_cost",
                "type": "DECIMAL(10,2)",
                "nullable": False,
                "range_check": ">= 0",
            },
            {
                "name": "margin_percentage",
                "type": "DECIMAL(5,2)",
                "nullable": False,
                "range_check": "BETWEEN 0 AND 100",
            },
            {"name": "is_active", "type": "BOOLEAN"},
        ],
        "primary_key": "cost_id",
        "foreign_keys": [
            {
                "column": "item_id",
                "references": {"table": "dim_item", "column": "item_id"},
            }
        ],
        "column_mapping_bronze_to_silver": {
            "cost_code": "cost_id",
            "item_code": "item_id",
            "cost_type": "cost_type",
            "sell_value": "selling_price",
            "buy_value": "purchase_cost",
            "margin_pct": "margin_percentage",
            "status": "is_active",
        },
    },
    "dim_site": {
        "schema": [
            {"name": "site_id", "type": "STRING", "nullable": False, "unique": True},
            {"name": "region_id", "type": "STRING"},
            {"name": "site_name", "type": "STRING", "nullable": False},
            {"name": "site_address", "type": "STRING"},
            {"name": "site_type", "type": "STRING", "nullable": False},
            {"name": "opening_date", "type": "DATE"},
            {"name": "manager_name", "type": "STRING"},
            {"name": "phone_number", "type": "STRING"},
            {"name": "manager_email", "type": "STRING"},
            {"name": "closing_date", "type": "DATE"},
            {"name": "operating_hours", "type": "STRING"},
            {"name": "is_active", "type": "BOOLEAN"},
        ],
        "primary_key": "site_id",
        "column_mapping_bronze_to_silver": {
            "site_code": "site_id",
            "region_code": "region_id",
            "name": "site_name",
            "location": "site_address",
            "type": "site_type",
            "opening_date": "opening_date",
            "mgr_name": "manager_name",
            "phone": "phone_number",
            "email": "manager_email",
            "closing_date": "closing_date",
            "operating_hours": "operating_hours",
            "status": "is_active",
        },
    },
    "dim_tax": {
        "schema": [
            {"name": "tax_id", "type": "STRING", "nullable": False, "unique": True},
            {"name": "tax_type", "type": "STRING", "nullable": False},
            {
                "name": "tax_rate",
                "type": "DECIMAL(5,2)",
                "range_check": "BETWEEN 0 AND 100",
            },
            {"name": "tax_region", "type": "STRING"},
            {"name": "tax_description", "type": "STRING"},
            {"name": "is_active", "type": "BOOLEAN"},
        ],
        "primary_key": "tax_id",
        "column_mapping_bronze_to_silver": {
            "tax_code": "tax_id",
            "tax_type": "tax_type",
            "tax_rate": "tax_rate",
            "tax_region": "tax_region",
            "tax_desc": "tax_description",
            "tax_status": "is_active",
        },
    },
    "dim_discount": {
        "schema": [
            {
                "name": "discount_id",
                "type": "STRING",
                "nullable": False,
                "unique": True,
            },
            {"name": "discount_type", "type": "STRING", "nullable": False},
            {
                "name": "discount_percentage",
                "type": "DECIMAL(5,2)",
                "range_check": "BETWEEN 0 AND 100",
            },
            {"name": "discount_description", "type": "STRING"},
            {"name": "start_date", "type": "DATE"},
            {"name": "end_date", "type": "DATE"},
            {"name": "is_active", "type": "BOOLEAN"},
        ],
        "primary_key": "discount_id",
        "column_mapping_bronze_to_silver": {
            "discount_code": "discount_id",
            "discount_type": "discount_type",
            "discount_pct": "discount_percentage",
            "discount_desc": "discount_description",
            "start_date": "start_date",
            "end_date": "end_date",
            "status": "is_active",
        },
    },
}