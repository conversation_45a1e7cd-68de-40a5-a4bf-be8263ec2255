# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace,
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType

# Initialize logger using configuration from startup
customer_silver_logger = create_logger(component_log_levels=component_log_levels)
customer_silver_logger.info("🚀 Initializing customer_silver notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

customer_silver_logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Schema Management Functions
@log_execution(customer_silver_logger)
def create_or_update_silver_table(spark, table_name, table_config, storage_path):
    """Create or update Delta table with complete schema, constraints, and properties."""
    customer_silver_logger.info(f"Managing schema for table {table_name}")

    try:
        # Get configuration
        base_schema = table_config["schema"]
        primary_key = table_config.get("primary_key")

        # Build complete DDL with all constraints
        column_definitions = []
        for col_def in base_schema:
            col_name = col_def["name"]
            col_type = col_def["type"]
            nullable = col_def.get("nullable", True)

            col_ddl = f"{col_name} {col_type}"
            if not nullable:
                col_ddl += " NOT NULL"
            column_definitions.append(col_ddl)

        # Add audit columns
        audit_columns = [
            "created_timestamp TIMESTAMP",
            "modified_timestamp TIMESTAMP",
            "processed_at TIMESTAMP",
            "source_system STRING",
            "data_quality_score DOUBLE",
            "primary_key_valid BOOLEAN",
            "completeness_score DOUBLE"
        ]

        all_columns = column_definitions + audit_columns
        schema_ddl = ",\n            ".join(all_columns)

        # Create complete table with constraints
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {schema_ddl},
            CONSTRAINT pk_{primary_key} PRIMARY KEY ({primary_key})
        )
        USING DELTA
        LOCATION '{storage_path}'
        TBLPROPERTIES (
            'delta.columnMapping.mode' = 'name',
            'delta.autoOptimize.optimizeWrite' = 'true',
            'delta.autoOptimize.autoCompact' = 'true',
            'delta.enableChangeDataFeed' = 'true'
        )
        """

        customer_silver_logger.info(f"Creating/updating table with DDL")
        spark.sql(create_sql)
        customer_silver_logger.info(f"✅ Table {table_name} ready with all constraints")

    except Exception as e:
        customer_silver_logger.error(f"Error managing table {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Simple Transformation Functions
@log_execution(customer_silver_logger)
def transform_customer_data(df):
    """Transform customer data with all business logic in one place."""
    customer_silver_logger.info("Applying customer transformations")

    try:
        # Get configuration
        customer_config = table_config["dim_customer"]
        column_mapping = customer_config["column_mapping_bronze_to_silver"]

        # Apply column mapping
        select_expressions = []
        for bronze_col, silver_col in column_mapping.items():
            if bronze_col in df.columns:
                select_expressions.append(col(bronze_col).alias(silver_col))

        if select_expressions:
            df = df.select(*select_expressions)

        # Apply all transformations using SQL expressions for simplicity
        df = df.select(
            # Core customer fields
            col("customer_id"),
            trim(regexp_replace(col("customer_name"), r"\s+", " ")).alias("customer_name"),

            # Standardize customer type
            when(upper(col("customer_type")).isin(["BUSINESS", "B"]), "Business")
            .when(upper(col("customer_type")).isin(["INDIVIDUAL", "I", "PERSON"]), "Individual")
            .when(upper(col("customer_type")).isin(["CORPORATE", "C", "CORP"]), "Corporate")
            .when(upper(col("customer_type")).isin(["GOVERNMENT", "G", "GOV"]), "Government")
            .when(upper(col("customer_type")).isin(["NON-PROFIT", "N", "NONPROFIT"]), "Non-profit")
            .otherwise(col("customer_type")).alias("customer_type"),

            # Clean email and validate
            lower(trim(col("customer_email_address"))).alias("customer_email_address"),

            # Standardize phone number to XXX-XXX-XXXX format
            regexp_replace(
                regexp_replace(col("phone_number").cast(StringType()), r"[^\d]", ""),
                r"(\d{3})(\d{3})(\d{4})", "$1-$2-$3"
            ).alias("phone_number"),

            # Clean address
            col("address"),

            # Convert registration date
            to_date(col("registration_date"), "M/d/yyyy").alias("registration_date"),

            # Convert is_active to boolean
            when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
            .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
            .otherwise(None).alias("is_active"),

            col("loyalty_card_id"),

            # Add validation columns
            (regexp_extract(lower(trim(col("customer_email_address"))), r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 0) != "").alias("email_valid"),

            (regexp_extract(
                regexp_replace(
                    regexp_replace(col("phone_number").cast(StringType()), r"[^\d]", ""),
                    r"(\d{3})(\d{3})(\d{4})", "$1-$2-$3"
                ), r"^\d{3}-\d{3}-\d{4}$", 0
            ) != "").alias("phone_valid"),

            # Extract address components
            regexp_extract(col("address"), r"[A-Z][0-9][A-Z]\s?[0-9][A-Z][0-9]", 0).alias("postal_code"),
            regexp_extract(col("address"), r"\b(AB|BC|MB|NB|NL|NS|NT|NU|ON|PE|QC|SK|YT)\b", 0).alias("province"),
            regexp_extract(col("address"), r", ([^,]+),", 1).alias("city"),

            # Calculate customer tenure
            when(col("registration_date").isNotNull(),
                 datediff(current_timestamp(), to_date(col("registration_date"), "M/d/yyyy"))
            ).otherwise(None).alias("customer_tenure_days"),

            # Data quality validation columns
            when(col("customer_id").isNotNull() & (col("customer_id") != ""), True).otherwise(False).alias("primary_key_valid"),
            when(col("customer_id").isNotNull() & (col("customer_id") != ""), True).otherwise(False).alias("customer_id_not_null_valid"),
            when(col("customer_name").isNotNull() & (col("customer_name") != ""), True).otherwise(False).alias("customer_name_not_null_valid"),
            when(col("customer_type").isNotNull() & (col("customer_type") != ""), True).otherwise(False).alias("customer_type_not_null_valid"),

            # Calculate completeness score based on required fields
            ((when(col("customer_id").isNotNull() & (col("customer_id") != ""), 1).otherwise(0) +
              when(col("customer_name").isNotNull() & (col("customer_name") != ""), 1).otherwise(0) +
              when(col("customer_type").isNotNull() & (col("customer_type") != ""), 1).otherwise(0)) / 3.0).alias("completeness_score"),

            # Calculate overall data quality score
            ((when(col("customer_id").isNotNull() & (col("customer_id") != ""), 1).otherwise(0) +
              when(col("customer_name").isNotNull() & (col("customer_name") != ""), 1).otherwise(0) +
              when((regexp_extract(lower(trim(col("customer_email_address"))), r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 0) != ""), 1).otherwise(0) +
              when(col("customer_type").isNotNull() & (col("customer_type") != ""), 1).otherwise(0)) / 4.0).alias("data_quality_score"),

            # Add audit columns
            current_timestamp().alias("created_timestamp"),
            current_timestamp().alias("modified_timestamp"),
            current_timestamp().alias("processed_at"),
            lit("bronze_layer").alias("source_system")
        )

        customer_silver_logger.info(f"Transformation completed. Columns: {len(df.columns)}")
        return df

    except Exception as e:
        customer_silver_logger.error(f"Error in transformation: {str(e)}")
        raise

# COMMAND ----------
# Data I/O Functions
@log_execution(customer_silver_logger)
def read_bronze_data(processing_date=None):
    """Read customer data from bronze layer."""
    customer_silver_logger.info("Reading bronze customer data")

    try:
        if processing_date:
            date_path = processing_date.replace('-', '/')
            source_path = f"{bronze_path}/customer/{date_path}"
            df = spark.read.format("parquet").load(source_path)
        else:
            source_path = f"{catalog}.{bronze_schema}.customer"
            df = spark.table(source_path)

        customer_silver_logger.info(f"Read {df.count()} records from bronze")
        return df

    except Exception as e:
        customer_silver_logger.error(f"Error reading bronze data: {str(e)}")
        raise

@log_execution(customer_silver_logger)
def write_silver_data(df, table_name):
    """Write customer data to silver layer with proper schema management."""
    customer_silver_logger.info(f"Writing data to silver table {table_name}")

    try:
        # Get configuration
        customer_config = table_config["dim_customer"]
        primary_key = customer_config["primary_key"]

        # Construct paths
        full_table_name = f"{catalog}.{silver_schema}.{table_name}"
        target_path = f"{silver_path}/{table_name}"

        # Create or update table schema
        customer_silver_logger.info("Ensuring table schema is up to date")
        create_or_update_silver_table(spark, full_table_name, customer_config, target_path)

        # Write data using merge for upsert behavior
        customer_silver_logger.info("Writing data with upsert logic")

        # Create temporary view for merge
        df.createOrReplaceTempView("temp_customer_data")

        # Use SQL MERGE for clean upsert
        merge_sql = f"""
        MERGE INTO {full_table_name} AS target
        USING temp_customer_data AS source
        ON target.{primary_key} = source.{primary_key}
        WHEN MATCHED THEN UPDATE SET *
        WHEN NOT MATCHED THEN INSERT *
        """

        spark.sql(merge_sql)
        customer_silver_logger.info("✅ Data written successfully with upsert")

        # Optimize table
        spark.sql(f"OPTIMIZE {full_table_name}")
        customer_silver_logger.info("✅ Table optimized")

        return df

    except Exception as e:
        customer_silver_logger.error(f"Error writing silver data: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function
@log_execution(customer_silver_logger)
def process_customer_bronze_to_silver(processing_date=None):
    """Simple main function to process customer data from bronze to silver."""
    customer_silver_logger.info("🚀 Starting customer bronze to silver processing")

    try:
        # Step 1: Read bronze data
        bronze_df = read_bronze_data(processing_date)
        if bronze_df.count() == 0:
            customer_silver_logger.warning("No bronze data found")
            return None

        # Step 2: Transform data
        silver_df = transform_customer_data(bronze_df)

        # Step 3: Write to silver layer
        write_silver_data(silver_df, "dim_customer")

        customer_silver_logger.info(f"✅ Successfully processed {silver_df.count()} customer records")
        return silver_df

    except Exception as e:
        customer_silver_logger.error(f"❌ Error processing customer data: {str(e)}")
        raise

# COMMAND ----------
# Main execution
customer_silver_logger.info("Starting customer bronze to silver processing")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process customer data using configuration
try:
    result_df = process_customer_bronze_to_silver(processing_date)
    if result_df is not None:
        customer_silver_logger.info("✅ Completed customer bronze to silver processing successfully")
        log_dataframe_info(result_df, "customer_silver_final", customer_silver_logger)
    else:
        customer_silver_logger.warning("⚠️ No data processed")
except Exception as e:
    customer_silver_logger.error(f"❌ Failed to process customer bronze to silver: {str(e)}")
    raise
