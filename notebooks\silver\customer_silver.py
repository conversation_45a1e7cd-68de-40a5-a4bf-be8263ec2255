# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace,
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType, IntegerType, BooleanType, DateType, TimestampType
from delta.tables import DeltaTable

# Initialize logger using configuration from startup
customer_silver_logger = create_logger(component_log_levels=component_log_levels)
customer_silver_logger.info("🚀 Initializing customer_silver notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

customer_silver_logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Table Creation Functions
@log_execution(customer_silver_logger)
def create_silver_table(spark, table_name, table_config, storage_path):
    """Create Delta table based on config with proper constraints."""
    customer_silver_logger.log_start(f"creating silver table {table_name}", "data_writing")

    try:
        # Get base schema from config
        base_schema = table_config["schema"]
        primary_key = table_config.get("primary_key")

        # Build schema definition with all constraints
        schema_parts = []
        constraints = []

        for col_def in base_schema:
            col_name = col_def["name"]
            col_type = col_def["type"]
            nullable = col_def.get("nullable", True)

            # Build column definition
            col_definition = f"{col_name} {col_type}"
            if not nullable:
                col_definition += " NOT NULL"

            schema_parts.append(col_definition)

            # Add check constraints if specified
            if "check_constraint" in col_def:
                constraint_name = f"chk_{col_name}"
                constraint_def = f"CONSTRAINT {constraint_name} CHECK ({col_def['check_constraint']})"
                constraints.append(constraint_def)

        # Add primary key constraint
        if primary_key:
            pk_constraint = f"CONSTRAINT pk_{primary_key} PRIMARY KEY ({primary_key})"
            constraints.append(pk_constraint)

        # Combine schema and constraints
        all_definitions = schema_parts + constraints
        schema_def = ",\n                ".join(all_definitions)

        customer_silver_logger.info(f"Creating table with primary key: {primary_key}")
        customer_silver_logger.info(f"Base schema columns: {len(base_schema)}")
        customer_silver_logger.info(f"Constraints: {len(constraints)}")

        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {schema_def}
            )
            USING DELTA
            LOCATION '{storage_path}'
            TBLPROPERTIES (
                'delta.columnMapping.mode' = 'name',
                'delta.autoOptimize.optimizeWrite' = 'true',
                'delta.autoOptimize.autoCompact' = 'true'
            )
        """

        customer_silver_logger.debug(f"Executing CREATE TABLE SQL: {create_sql}")
        spark.sql(create_sql)

        customer_silver_logger.info(f"Successfully created table {table_name}")

    except Exception as e:
        customer_silver_logger.error(f"Error creating table {table_name}: {str(e)}")
        raise

@log_execution(customer_silver_logger)
def apply_column_mapping(df, column_mapping):
    """Apply column mapping from bronze to silver schema."""
    customer_silver_logger.debug(f"Applying column mapping: {column_mapping}")

    # Select and rename columns based on mapping
    select_expressions = []
    for bronze_col, silver_col in column_mapping.items():
        if bronze_col in df.columns:
            select_expressions.append(col(bronze_col).alias(silver_col))
        else:
            customer_silver_logger.warning(f"Column {bronze_col} not found in DataFrame")

    if select_expressions:
        df = df.select(*select_expressions)
        customer_silver_logger.info(f"Applied column mapping, resulting columns: {df.columns}")

    return df

@log_execution(customer_silver_logger)
def validate_email_format(df, email_column):
    """Validate email format and add validation flag."""
    if email_column in df.columns:
        customer_silver_logger.debug(f"Validating email format for column: {email_column}")
        df = df.withColumn(
            "email_valid",
            regexp_extract(col(email_column), r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 0) != ""
        )
    return df

@log_execution(customer_silver_logger)
def standardize_phone_numbers(df, phone_column):
    """Standardize phone number format."""
    if phone_column in df.columns:
        customer_silver_logger.debug(f"Standardizing phone numbers for column: {phone_column}")
        # Remove all non-numeric characters and format as XXX-XXX-XXXX
        df = df.withColumn(
            phone_column,
            regexp_replace(
                regexp_replace(col(phone_column), r"[^\d]", ""),
                r"(\d{3})(\d{3})(\d{4})",
                "$1-$2-$3"
            )
        )
    return df

@log_execution(customer_silver_logger)
def validate_data_quality(df):
    """Perform data quality validations and add quality score."""
    customer_silver_logger.debug("Performing data quality validations")
    
    # Calculate quality score based on completeness
    quality_checks = []
    
    # Check for non-null customer_id (primary key)
    if "customer_id" in df.columns:
        quality_checks.append(when(col("customer_id").isNotNull() & (col("customer_id") != ""), 1).otherwise(0))
    
    # Check for non-null customer_name
    if "customer_name" in df.columns:
        quality_checks.append(when(col("customer_name").isNotNull() & (col("customer_name") != ""), 1).otherwise(0))
    
    # Check for valid email format
    if "email_valid" in df.columns:
        quality_checks.append(when(col("email_valid") == True, 1).otherwise(0))
    
    # Check for non-null customer_type
    if "customer_type" in df.columns:
        quality_checks.append(when(col("customer_type").isNotNull() & (col("customer_type") != ""), 1).otherwise(0))
    
    if quality_checks:
        # Calculate average quality score (0-1)
        total_checks = len(quality_checks)
        df = df.withColumn(
            "data_quality_score",
            (sum(quality_checks) / total_checks)
        )
    
    return df

# COMMAND ----------
# Data Transformation Functions
@log_execution(customer_silver_logger)
def validate_schema_constraints(df, table_config):
    """Validate schema constraints and add constraint validation columns."""
    customer_silver_logger.debug("Validating schema constraints")

    schema_def = table_config["schema"]
    primary_key = table_config["primary_key"]

    # Validate primary key constraint
    if primary_key in df.columns:
        df = df.withColumn(
            "primary_key_valid",
            col(primary_key).isNotNull() & (col(primary_key) != "")
        )
    else:
        df = df.withColumn("primary_key_valid", lit(False))

    # Validate NOT NULL constraints
    for col_def in schema_def:
        col_name = col_def["name"]
        if not col_def.get("nullable", True) and col_name in df.columns:
            # Add validation for non-nullable columns
            df = df.withColumn(
                f"{col_name}_not_null_valid",
                col(col_name).isNotNull() & (col(col_name) != "")
            )

    # Calculate completeness score based on required fields
    required_fields = [col_def["name"] for col_def in schema_def if not col_def.get("nullable", True)]
    if required_fields:
        completeness_checks = []
        for field in required_fields:
            if field in df.columns:
                completeness_checks.append(
                    when(col(field).isNotNull() & (col(field) != ""), 1).otherwise(0)
                )

        if completeness_checks:
            df = df.withColumn(
                "completeness_score",
                (sum(completeness_checks) / len(completeness_checks))
            )

    return df

@log_execution(customer_silver_logger)
def ensure_schema_columns(df, table_config):
    """Ensure all schema columns are present in the DataFrame."""
    customer_silver_logger.debug("Ensuring all schema columns are present")

    schema_def = table_config["schema"]
    existing_columns = df.columns

    # Add missing schema columns with default values
    for col_def in schema_def:
        col_name = col_def["name"]
        col_type = col_def["type"]

        if col_name not in existing_columns:
            customer_silver_logger.warning(f"Adding missing schema column: {col_name}")

            # Add column with appropriate default value based on type
            if col_type == "STRING":
                df = df.withColumn(col_name, lit(None).cast(StringType()))
            elif col_type == "INT":
                df = df.withColumn(col_name, lit(None).cast(IntegerType()))
            elif col_type == "BOOLEAN":
                df = df.withColumn(col_name, lit(None).cast(BooleanType()))
            elif col_type == "DATE":
                df = df.withColumn(col_name, lit(None).cast(DateType()))
            elif col_type == "TIMESTAMP":
                df = df.withColumn(col_name, lit(None).cast(TimestampType()))
            else:
                df = df.withColumn(col_name, lit(None).cast(StringType()))
        else:
            # Column exists - ensure it matches expected type (only for critical mismatches)
            # Skip phone_number as it's already been transformed to STRING format
            if col_name == "phone_number" and col_type == "STRING":
                # Phone number has already been standardized to STRING format, keep as is
                customer_silver_logger.debug(f"Column {col_name} already transformed to STRING format")
                continue

    customer_silver_logger.info(f"Schema validation complete. Total columns: {len(df.columns)}")
    return df

@log_execution(customer_silver_logger)
def transform_customer_data(df):
    """Apply comprehensive transformations to customer data."""
    customer_silver_logger.log_start("transforming customer data", "transformation")

    try:
        # Get customer table configuration
        customer_config = table_config["dim_customer"]
        column_mapping = customer_config["column_mapping_bronze_to_silver"]

        # Apply column mapping
        df = apply_column_mapping(df, column_mapping)

        # Data type conversions and standardizations first
        customer_silver_logger.debug("Applying data type conversions and standardizations")

        # Convert registration_date to proper date format
        if "registration_date" in df.columns:
            df = df.withColumn(
                "registration_date",
                to_date(col("registration_date"), "M/d/yyyy")
            )

        # Convert is_active to boolean
        if "is_active" in df.columns:
            df = df.withColumn(
                "is_active",
                when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
                .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
                .otherwise(None)
            )
        
        # Standardize customer_type
        if "customer_type" in df.columns:
            df = df.withColumn(
                "customer_type",
                when(upper(col("customer_type")).isin(["BUSINESS", "B"]), "Business")
                .when(upper(col("customer_type")).isin(["INDIVIDUAL", "I", "PERSON"]), "Individual")
                .when(upper(col("customer_type")).isin(["CORPORATE", "C", "CORP"]), "Corporate")
                .when(upper(col("customer_type")).isin(["GOVERNMENT", "G", "GOV"]), "Government")
                .when(upper(col("customer_type")).isin(["NON-PROFIT", "N", "NONPROFIT"]), "Non-profit")
                .otherwise(col("customer_type"))
            )

        # Clean and standardize customer_name
        if "customer_name" in df.columns:
            df = df.withColumn(
                "customer_name",
                trim(regexp_replace(col("customer_name"), r"\s+", " "))
            )

        # Validate and standardize email
        if "customer_email_address" in df.columns:
            df = df.withColumn(
                "customer_email_address",
                lower(trim(col("customer_email_address")))
            )
            df = validate_email_format(df, "customer_email_address")

        # Standardize phone numbers and ensure correct data type
        if "phone_number" in df.columns:
            # First convert to string if it's not already
            df = df.withColumn("phone_number", col("phone_number").cast(StringType()))
            df = standardize_phone_numbers(df, "phone_number")
            # Add phone validation flag
            df = df.withColumn(
                "phone_valid",
                regexp_extract(col("phone_number"), r"^\d{3}-\d{3}-\d{4}$", 0) != ""
            )

        # Extract address components if address exists
        if "address" in df.columns:
            # Extract postal code (Canadian format: A1A 1A1)
            df = df.withColumn(
                "postal_code",
                regexp_extract(col("address"), r"[A-Z][0-9][A-Z]\s?[0-9][A-Z][0-9]", 0)
            )

            # Extract province (Canadian province codes)
            df = df.withColumn(
                "province",
                regexp_extract(col("address"), r"\b(AB|BC|MB|NB|NL|NS|NT|NU|ON|PE|QC|SK|YT)\b", 0)
            )

            # Extract city (assuming format: "123 Main St, City, Province A1A 1A1")
            df = df.withColumn(
                "city",
                regexp_extract(col("address"), r", ([^,]+),", 1)
            )

        # Calculate customer tenure if registration_date exists
        if "registration_date" in df.columns:
            df = df.withColumn(
                "customer_tenure_days",
                datediff(current_timestamp(), col("registration_date"))
            )

        # Ensure all schema columns are present (after transformations)
        df = ensure_schema_columns(df, customer_config)

        # Validate schema constraints
        df = validate_schema_constraints(df, customer_config)

        # Perform data quality validations
        df = validate_data_quality(df)

        # Add audit columns
        df = df.withColumn("created_timestamp", current_timestamp())
        df = df.withColumn("modified_timestamp", current_timestamp())
        df = df.withColumn("processed_at", current_timestamp())
        df = df.withColumn("source_system", lit("bronze_layer"))

        # Log DataFrame information
        log_dataframe_info(df, "customer_transformed", customer_silver_logger, "transformation")
        return df

    except Exception as e:
        customer_silver_logger.error(f"Error transforming customer data: {str(e)}")
        raise

# COMMAND ----------
# Data Reading and Writing Functions
@log_execution(customer_silver_logger)
def read_bronze_customer_data(processing_date=None):
    """Read customer data from bronze layer."""
    customer_silver_logger.log_start("reading bronze customer data", "data_loading")
    
    try:
        # Construct bronze table path
        if processing_date:
            # Read specific date partition
            date_path = processing_date.replace('-', '/')
            source_path = f"{bronze_path}/customer/{date_path}"
            customer_silver_logger.info(f"Reading bronze data from date partition: {source_path}")
        else:
            # Read from bronze table
            source_path = f"{catalog}.{bronze_schema}.customer"
            customer_silver_logger.info(f"Reading bronze data from table: {source_path}")
        
        # Read the data
        if processing_date:
            df = spark.read.format("parquet").load(source_path)
        else:
            df = spark.table(source_path)
        
        # Log DataFrame information
        log_dataframe_info(df, "customer_bronze", customer_silver_logger, "data_loading")
        return df
        
    except Exception as e:
        customer_silver_logger.error(f"Error reading bronze customer data: {str(e)}")
        raise

@log_execution(customer_silver_logger)
def write_silver_customer_data(df, table_name):
    """Write customer data to silver layer."""
    customer_silver_logger.log_start(f"writing customer data to silver table {table_name}", "data_writing")
    
    try:
        # Get customer configuration
        customer_config = table_config["dim_customer"]
        primary_key = customer_config["primary_key"]
        
        # Construct target paths
        full_table_name = f"{catalog}.{silver_schema}.{table_name}"
        target_path = f"{silver_path}/{table_name}"
        
        customer_silver_logger.info(f"Writing to table: {full_table_name}")
        customer_silver_logger.info(f"Target path: {target_path}")
        customer_silver_logger.info(f"Primary key: {primary_key}")
        
        # Check if table exists
        table_exists = spark.catalog.tableExists(full_table_name)
        customer_silver_logger.info(f"Table exists: {table_exists}")
        
        if not table_exists:
            # Create table with base schema first (constraints and primary key)
            customer_silver_logger.info("Creating new table with base schema and constraints")
            create_silver_table(spark, full_table_name, customer_config, target_path)

            # Initial write with mergeSchema to add audit and DQ columns automatically
            customer_silver_logger.info("Performing initial write with mergeSchema for audit and DQ columns")
            df.write.format("delta") \
                .mode("overwrite") \
                .option("mergeSchema", "true") \
                .option("path", target_path) \
                .saveAsTable(full_table_name)

            customer_silver_logger.info("Initial data load completed with schema evolution")

        else:
            # For existing tables, always use merge operation to prevent duplicates
            customer_silver_logger.info("Table exists - performing upsert operation to prevent duplicates")

            # Handle schema evolution first if needed
            try:
                # Check if new columns exist in source but not in target
                target_columns = set(spark.table(full_table_name).columns)
                source_columns = set(df.columns)
                new_columns = source_columns - target_columns

                if new_columns:
                    customer_silver_logger.info(f"New columns detected: {new_columns}")
                    customer_silver_logger.info("Performing schema evolution with mergeSchema")

                    # Use empty DataFrame with mergeSchema to add new columns
                    df.filter("1=0").write.format("delta") \
                        .mode("append") \
                        .option("mergeSchema", "true") \
                        .option("path", target_path) \
                        .saveAsTable(full_table_name)

                    customer_silver_logger.info("Schema evolution completed")

            except Exception as schema_error:
                customer_silver_logger.warning(f"Schema evolution check failed: {str(schema_error)}")

            # Create Delta table reference
            delta_table = DeltaTable.forName(spark, full_table_name)

            # Perform merge operation (upsert) to prevent duplicates
            merge_condition = f"target.{primary_key} = source.{primary_key}"

            customer_silver_logger.info(f"Performing merge with condition: {merge_condition}")
            delta_table.alias("target").merge(
                df.alias("source"),
                merge_condition
            ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()

            customer_silver_logger.info("Upsert operation completed - no duplicates created")

        # Optimize table
        customer_silver_logger.log_start(f"optimizing table {full_table_name}", "data_writing")
        spark.sql(f"OPTIMIZE {full_table_name}")
        customer_silver_logger.info(f"Successfully optimized table {full_table_name}")

        return df
        
    except Exception as e:
        customer_silver_logger.error(f"Error writing silver customer data: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function
@log_execution(customer_silver_logger)
def process_customer_bronze_to_silver(processing_date=None):
    """Main function to process customer data from bronze to silver layer."""
    customer_silver_logger.log_start("processing customer bronze to silver", "transformation")

    table_name = "dim_customer"
    customer_silver_logger.info(f"Processing table: {table_name}")
    customer_silver_logger.info(f"Processing date: {processing_date if processing_date else 'all data'}")

    try:
        # Read bronze data
        bronze_df = read_bronze_customer_data(processing_date)

        if bronze_df is None or bronze_df.count() == 0:
            customer_silver_logger.warning(f"No bronze data found for customer on date {processing_date}")
            return None

        # Transform data
        silver_df = transform_customer_data(bronze_df)

        # Write to silver layer
        final_df = write_silver_customer_data(silver_df, table_name)

        customer_silver_logger.info(f"Successfully processed {final_df.count()} customer records")
        return final_df

    except Exception as e:
        customer_silver_logger.error(f"Error processing customer bronze to silver: {str(e)}")
        raise

# COMMAND ----------
# Main execution
customer_silver_logger.info("Starting customer bronze to silver processing")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process customer data using configuration
try:
    result_df = process_customer_bronze_to_silver(processing_date)
    if result_df is not None:
        customer_silver_logger.info("✅ Completed customer bronze to silver processing successfully")
        log_dataframe_info(result_df, "customer_silver_final", customer_silver_logger)
    else:
        customer_silver_logger.warning("⚠️ No data processed")
except Exception as e:
    customer_silver_logger.error(f"❌ Failed to process customer bronze to silver: {str(e)}")
    raise
