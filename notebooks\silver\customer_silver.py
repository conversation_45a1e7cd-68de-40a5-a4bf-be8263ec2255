# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# Imports
from pyspark.sql import SparkSession
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace,
    to_timestamp, to_date, regexp_extract, isnan, isnull, length, coalesce,
    datediff, expr, concat, concat_ws
)
from pyspark.sql.types import StringType, IntegerType, BooleanType, DateType, TimestampType
from delta.tables import DeltaTable

# Initialize logger using configuration from startup
customer_silver_logger = create_logger(component_log_levels=component_log_levels)
customer_silver_logger.info("🚀 Initializing customer_silver notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

customer_silver_logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Utility Functions
@log_execution(customer_silver_logger)
def get_table_config(table_name, config):
    """Retrieve table configuration from provided config dictionary."""
    customer_silver_logger.debug(f"Retrieving configuration for table: {table_name}")
    return config.get(table_name, {})

@log_execution(customer_silver_logger)
def apply_column_mapping(df, column_mapping):
    """Apply column mapping from bronze to silver schema."""
    customer_silver_logger.debug(f"Applying column mapping: {column_mapping}")
    
    # Select and rename columns based on mapping
    select_expressions = []
    for bronze_col, silver_col in column_mapping.items():
        if bronze_col in df.columns:
            select_expressions.append(col(bronze_col).alias(silver_col))
        else:
            customer_silver_logger.warning(f"Column {bronze_col} not found in DataFrame")
    
    if select_expressions:
        df = df.select(*select_expressions)
        customer_silver_logger.info(f"Applied column mapping, resulting columns: {df.columns}")
    
    return df

@log_execution(customer_silver_logger)
def validate_email_format(df, email_column):
    """Validate email format and add validation flag."""
    if email_column in df.columns:
        customer_silver_logger.debug(f"Validating email format for column: {email_column}")
        df = df.withColumn(
            f"{email_column}_valid",
            regexp_extract(col(email_column), r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 0) != ""
        )
    return df

@log_execution(customer_silver_logger)
def standardize_phone_numbers(df, phone_column):
    """Standardize phone number format."""
    if phone_column in df.columns:
        customer_silver_logger.debug(f"Standardizing phone numbers for column: {phone_column}")
        # Remove all non-numeric characters and format as XXX-XXX-XXXX
        df = df.withColumn(
            phone_column,
            regexp_replace(
                regexp_replace(col(phone_column), r"[^\d]", ""),
                r"(\d{3})(\d{3})(\d{4})",
                "$1-$2-$3"
            )
        )
    return df

@log_execution(customer_silver_logger)
def validate_data_quality(df):
    """Perform data quality validations and add quality score."""
    customer_silver_logger.debug("Performing data quality validations")
    
    # Calculate quality score based on completeness
    quality_checks = []
    
    # Check for non-null customer_id (primary key)
    if "customer_id" in df.columns:
        quality_checks.append(when(col("customer_id").isNotNull() & (col("customer_id") != ""), 1).otherwise(0))
    
    # Check for non-null customer_name
    if "customer_name" in df.columns:
        quality_checks.append(when(col("customer_name").isNotNull() & (col("customer_name") != ""), 1).otherwise(0))
    
    # Check for valid email format
    if "customer_email_address_valid" in df.columns:
        quality_checks.append(when(col("customer_email_address_valid") == True, 1).otherwise(0))
    
    # Check for non-null customer_type
    if "customer_type" in df.columns:
        quality_checks.append(when(col("customer_type").isNotNull() & (col("customer_type") != ""), 1).otherwise(0))
    
    if quality_checks:
        # Calculate average quality score (0-1)
        total_checks = len(quality_checks)
        df = df.withColumn(
            "data_quality_score",
            (sum(quality_checks) / total_checks)
        )
    
    return df

# COMMAND ----------
# Data Transformation Functions
@log_execution(customer_silver_logger)
def transform_customer_data(df):
    """Apply comprehensive transformations to customer data."""
    customer_silver_logger.log_start("transforming customer data", "transformation")
    
    try:
        # Get customer table configuration
        customer_config = table_config["dim_customer"]
        column_mapping = customer_config["column_mapping_bronze_to_silver"]
        
        # Apply column mapping
        df = apply_column_mapping(df, column_mapping)
        
        # Data type conversions
        customer_silver_logger.debug("Applying data type conversions")
        
        # Convert registration_date to proper date format
        if "registration_date" in df.columns:
            df = df.withColumn(
                "registration_date",
                to_date(col("registration_date"), "M/d/yyyy")
            )
        
        # Convert is_active to boolean
        if "is_active" in df.columns:
            df = df.withColumn(
                "is_active",
                when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
                .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
                .otherwise(None)
            )
        
        # Standardize customer_type
        if "customer_type" in df.columns:
            df = df.withColumn(
                "customer_type",
                when(upper(col("customer_type")).isin(["BUSINESS", "B"]), "Business")
                .when(upper(col("customer_type")).isin(["INDIVIDUAL", "I", "PERSON"]), "Individual")
                .when(upper(col("customer_type")).isin(["CORPORATE", "C", "CORP"]), "Corporate")
                .when(upper(col("customer_type")).isin(["GOVERNMENT", "G", "GOV"]), "Government")
                .when(upper(col("customer_type")).isin(["NON-PROFIT", "N", "NONPROFIT"]), "Non-profit")
                .otherwise(col("customer_type"))
            )
        
        # Clean and standardize customer_name
        if "customer_name" in df.columns:
            df = df.withColumn(
                "customer_name",
                trim(regexp_replace(col("customer_name"), r"\s+", " "))
            )
        
        # Validate and standardize email
        if "customer_email_address" in df.columns:
            df = df.withColumn(
                "customer_email_address",
                lower(trim(col("customer_email_address")))
            )
            df = validate_email_format(df, "customer_email_address")
        
        # Standardize phone numbers
        if "phone_number" in df.columns:
            df = standardize_phone_numbers(df, "phone_number")
        
        # Calculate customer tenure if registration_date exists
        if "registration_date" in df.columns:
            df = df.withColumn(
                "customer_tenure_days",
                datediff(current_timestamp(), col("registration_date"))
            )
        
        # Perform data quality validations
        df = validate_data_quality(df)
        
        # Add processing metadata
        df = df.withColumn("processed_at", current_timestamp())
        df = df.withColumn("source_system", lit("bronze_layer"))
        
        # Log DataFrame information
        log_dataframe_info(df, "customer_transformed", customer_silver_logger, "transformation")
        return df
        
    except Exception as e:
        customer_silver_logger.error(f"Error transforming customer data: {str(e)}")
        raise

# COMMAND ----------
# Data Reading and Writing Functions
@log_execution(customer_silver_logger)
def read_bronze_customer_data(processing_date=None):
    """Read customer data from bronze layer."""
    customer_silver_logger.log_start("reading bronze customer data", "data_loading")
    
    try:
        # Construct bronze table path
        if processing_date:
            # Read specific date partition
            date_path = processing_date.replace('-', '/')
            source_path = f"{bronze_path}/customer/{date_path}"
            customer_silver_logger.info(f"Reading bronze data from date partition: {source_path}")
        else:
            # Read from bronze table
            source_path = f"{catalog}.{bronze_schema}.customer"
            customer_silver_logger.info(f"Reading bronze data from table: {source_path}")
        
        # Read the data
        if processing_date:
            df = spark.read.format("parquet").load(source_path)
        else:
            df = spark.table(source_path)
        
        # Log DataFrame information
        log_dataframe_info(df, "customer_bronze", customer_silver_logger, "data_loading")
        return df
        
    except Exception as e:
        customer_silver_logger.error(f"Error reading bronze customer data: {str(e)}")
        raise

@log_execution(customer_silver_logger)
def write_silver_customer_data(df, table_name):
    """Write customer data to silver layer."""
    customer_silver_logger.log_start(f"writing customer data to silver table {table_name}", "data_writing")
    
    try:
        # Get customer configuration
        customer_config = table_config["dim_customer"]
        primary_key = customer_config["primary_key"]
        
        # Construct target paths
        full_table_name = f"{catalog}.{silver_schema}.{table_name}"
        target_path = f"{silver_path}/{table_name}"
        
        customer_silver_logger.info(f"Writing to table: {full_table_name}")
        customer_silver_logger.info(f"Target path: {target_path}")
        customer_silver_logger.info(f"Primary key: {primary_key}")
        
        # Check if table exists
        table_exists = spark.catalog.tableExists(full_table_name)
        customer_silver_logger.info(f"Table exists: {table_exists}")
        
        if table_exists:
            # Perform upsert operation
            customer_silver_logger.info("Performing upsert operation")
            
            # Create Delta table reference
            delta_table = DeltaTable.forName(spark, full_table_name)
            
            # Perform merge operation
            merge_condition = f"target.{primary_key} = source.{primary_key}"
            
            delta_table.alias("target").merge(
                df.alias("source"),
                merge_condition
            ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
            
            customer_silver_logger.info("Upsert operation completed")
            
        else:
            # Create new table
            customer_silver_logger.info("Creating new table")
            
            df.write.format("delta") \
                .mode("overwrite") \
                .option("path", target_path) \
                .saveAsTable(full_table_name)
            
            customer_silver_logger.info(f"Created new table: {full_table_name}")
        
        # Optimize table
        customer_silver_logger.log_start(f"optimizing table {full_table_name}", "data_writing")
        spark.sql(f"OPTIMIZE {full_table_name}")
        customer_silver_logger.info(f"Successfully optimized table {full_table_name}")
        
        return df
        
    except Exception as e:
        customer_silver_logger.error(f"Error writing silver customer data: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function
@log_execution(customer_silver_logger)
def process_customer_bronze_to_silver(processing_date=None):
    """Main function to process customer data from bronze to silver layer."""
    customer_silver_logger.log_start("processing customer bronze to silver", "transformation")

    table_name = "dim_customer"
    customer_silver_logger.info(f"Processing table: {table_name}")
    customer_silver_logger.info(f"Processing date: {processing_date if processing_date else 'all data'}")

    try:
        # Read bronze data
        bronze_df = read_bronze_customer_data(processing_date)

        if bronze_df is None or bronze_df.count() == 0:
            customer_silver_logger.warning(f"No bronze data found for customer on date {processing_date}")
            return None

        # Transform data
        silver_df = transform_customer_data(bronze_df)

        # Write to silver layer
        final_df = write_silver_customer_data(silver_df, table_name)

        customer_silver_logger.info(f"Successfully processed {final_df.count()} customer records")
        return final_df

    except Exception as e:
        customer_silver_logger.error(f"Error processing customer bronze to silver: {str(e)}")
        raise

# COMMAND ----------
# Main execution
customer_silver_logger.info("Starting customer bronze to silver processing")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process customer data using configuration
try:
    result_df = process_customer_bronze_to_silver(processing_date)
    if result_df is not None:
        customer_silver_logger.info("✅ Completed customer bronze to silver processing successfully")
        log_dataframe_info(result_df, "customer_silver_final", customer_silver_logger)
    else:
        customer_silver_logger.warning("⚠️ No data processed")
except Exception as e:
    customer_silver_logger.error(f"❌ Failed to process customer bronze to silver: {str(e)}")
    raise
