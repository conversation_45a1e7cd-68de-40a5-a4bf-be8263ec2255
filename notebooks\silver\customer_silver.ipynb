{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "cce6766b-9aee-4c17-8df7-5b39b45b9db0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Customer - Bronze to Silver Transformation\n", "\n", "This notebook processes customer data from the bronze layer to the silver layer. It applies data type conversions, handles timestamp formatting, standardizes values, and performs data quality checks."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../utils/silver_utils"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../utils/schema_utils"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa06b87d-8138-445a-b0d4-39d2819f695d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import (\n", "    input_file_name, current_timestamp, lit, col, to_timestamp, when, \n", "    upper, regexp_extract, datediff, expr\n", ")\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "\n", "# Use the logger configuration from startup with log_path from config\n", "customer_silver_logger = create_logger(component_log_levels=component_log_levels)\n", "customer_silver_logger.info(\"Initializing notebook\")\n", " \n", "# Extract frequently used config values into variables\n", "catalog = pipeline_config[\"catalog\"]\n", "target_date = pipeline_config[\"default_processing_date\"]\n", "bronze_schema = pipeline_config[\"schemas\"][\"bronze\"]\n", "silver_schema = pipeline_config[\"schemas\"][\"silver\"]\n", "bronze_path = pipeline_config[\"paths\"][\"bronze_path\"]\n", "silver_path = pipeline_config[\"paths\"][\"silver_path\"]\n", "silver_format = pipeline_config[\"file_formats\"][\"silver\"]\n", "delta_properties = pipeline_config[\"delta_properties\"]\n", "\n", "# Switch to catalog\n", "spark.sql(f\"USE CATALOG {catalog}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d3fbae4c-e504-40ad-bd0e-54035b2beaf1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Get customer table configuration from table_config\n", "customer_config = table_config[\"tables\"][\"customer\"]\n", "customer_silver_logger.info(f\"Loaded customer configuration from table_config\")\n", "\n", "# Table metadata for customer table\n", "table_metadata = {\n", "    \"primary_key\": customer_config[\"silver\"][\"primary_key\"],\n", "    \"column_mapping\": customer_config[\"silver\"][\"column_mapping_bronze_to_silver\"],\n", "    \"timestamp_columns\": [\"registration_date\", \"created_timestamp\", \"modified_timestamp\"],\n", "    \"numeric_columns\": [\"primary_key\"],\n", "    \"boolean_columns\": [\"is_active\"],\n", "    \"customer_types\": [\"Business\", \"Individual\", \"Corporate\", \"Government\", \"Non-profit\"],\n", "    \"loyalty_tiers\": [\"Bronze\", \"Silver\", \"Gold\", \"Platinum\"]\n", "}\n", "\n", "# Log the column mapping for debugging\n", "customer_silver_logger.info(f\"Column mapping: {table_metadata['column_mapping']}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "234247ab-033a-4431-81ad-209eb7e59a91", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to apply transformations based on table metadata\n", "@log_execution(customer_silver_logger)\n", "def apply_transformations(df):\n", "    customer_silver_logger.info(\"Applying transformations to customer data\")\n", "    \n", "    # Apply column mapping from bronze to silver\n", "    column_mapping = table_metadata[\"column_mapping\"]\n", "    df = apply_column_mapping(df, column_mapping)\n", "    \n", "    # Get the schema for customer table in silver layer\n", "    customer_schema = get_schema(\"customer\", \"silver\")\n", "    \n", "    # Apply schema to enforce data types\n", "    if customer_schema:\n", "        customer_silver_logger.info(\"Applying explicit schema for data type enforcement\")\n", "        df = apply_schema(df, customer_schema)\n", "    else:\n", "        # Fall back to individual conversions if schema is not available\n", "        customer_silver_logger.info(\"Falling back to individual data type conversions\")\n", "        # Apply timestamp conversions\n", "        df = convert_timestamp_columns(df, table_metadata[\"timestamp_columns\"], \"M/d/yyyy\")\n", "        \n", "        # Apply numeric conversions\n", "        df = convert_numeric_columns(df, table_metadata[\"numeric_columns\"])\n", "        \n", "        # Apply boolean conversions\n", "        df = convert_boolean_columns(df, table_metadata[\"boolean_columns\"])\n", "    \n", "    # Validate email format\n", "    if \"customer_email_address\" in df.columns:\n", "        df = validate_regex_pattern(\n", "            df, \n", "            \"customer_email_address\", \n", "            \"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$\",\n", "            \"email_valid\"\n", "        )\n", "    \n", "    # Validate phone number format and standardize\n", "    if \"phone_number\" in df.columns:\n", "        df = standardize_phone_numbers(df, [\"phone_number\"])\n", "        df = validate_regex_pattern(\n", "            df, \n", "            \"phone_number\", \n", "            \"^[0-9]{3}-[0-9]{3}-[0-9]{4}$\",\n", "            \"phone_valid\"\n", "        )\n", "    \n", "    # Standardize and validate customer type\n", "    if \"customer_type\" in df.columns:\n", "        df = standardize_categorical_values(\n", "            df, \n", "            \"customer_type\", \n", "            table_metadata[\"customer_types\"]\n", "        )\n", "    \n", "    # Standardize and validate loyalty tier\n", "    if \"loyalty_tier\" in df.columns:\n", "        df = standardize_categorical_values(\n", "            df, \n", "            \"loyalty_tier\", \n", "            table_metadata[\"loyalty_tiers\"]\n", "        )\n", "    \n", "    # Extract address components\n", "    if \"address\" in df.columns:\n", "        # Extract postal code (assuming Canadian format: A1A 1A1)\n", "        df = df.withColumn(\n", "            \"postal_code\", \n", "            regexp_extract(col(\"address\"), \"[A-Z][0-9][A-Z]\\\\s?[0-9][A-Z][0-9]\", 0)\n", "        )\n", "        \n", "        # Extract province (assuming Canadian province codes)\n", "        df = df.withColumn(\n", "            \"province\", \n", "            regexp_extract(col(\"address\"), \"\\\\b(AB|BC|MB|NB|NL|NS|NT|NU|ON|PE|QC|SK|YT)\\\\b\", 0)\n", "        )\n", "        \n", "        # Extract city (assuming format: \"123 Main St, City, Province A1A 1A1\")\n", "        df = df.withColumn(\n", "            \"city\", \n", "            regexp_extract(col(\"address\"), \", ([^,]+),\", 1)\n", "        )\n", "    \n", "    # Calculate customer tenure in days\n", "    if \"registration_date\" in df.columns:\n", "        df = df.withColumn(\n", "            \"customer_tenure_days\",\n", "            datediff(current_timestamp(), col(\"registration_date\"))\n", "        )\n", "    \n", "    # Validate primary key\n", "    df, pk_valid = validate_primary_key(df, table_metadata[\"primary_key\"])\n", "    if not pk_valid:\n", "        customer_silver_logger.warning(f\"Primary key validation failed for {table_metadata['primary_key']}\")\n", "    \n", "    # Calculate data quality score\n", "    df = calculate_data_quality_score(df)\n", "    \n", "    # Add processing timestamp\n", "    df = df.withColumn(\"processed_at\", current_timestamp())\n", "    \n", "    return log_dataframe_info(df, \"customer_transformed\", customer_silver_logger)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Main processing function\n", "@log_execution(customer_silver_logger)\n", "def process_bronze_to_silver(target_date=None):\n", "    customer_silver_logger.info(f\"Processing customer data from bronze to silver for date: {target_date if target_date else 'all'}\")\n", "    \n", "    # Get the schema for customer table in silver layer\n", "    customer_schema = get_schema(\"customer\", \"silver\")\n", "    customer_silver_logger.info(f\"Using explicit schema for customer table: {customer_schema}\")\n", "    \n", "    # Read bronze data directly using the utility function\n", "    bronze_df = read_bronze_data(\n", "        spark=spark,\n", "        bronze_path=bronze_path,\n", "        table_name=\"customer\",\n", "        date=target_date,\n", "        logger=customer_silver_logger\n", "    )\n", "    \n", "    if bronze_df is None or bronze_df.isEmpty():\n", "        customer_silver_logger.warning(f\"No bronze data found for customer on date {target_date}\")\n", "        return None\n", "    \n", "    # Apply transformations\n", "    silver_df = apply_transformations(bronze_df)\n", "    \n", "    # Write to silver layer directly using the utility function\n", "    final_df = write_silver_data(\n", "        spark=spark,\n", "        df=silver_df,\n", "        silver_path=silver_path,\n", "        table_name=\"customer_cleaned\",\n", "        primary_key=table_metadata[\"primary_key\"],\n", "        column_mapping=None,  # Don't apply column mapping again as it's already applied in apply_transformations\n", "        catalog=catalog,\n", "        schema=silver_schema,\n", "        format=silver_format,\n", "        logger=customer_silver_logger\n", "    )\n", "    \n", "    customer_silver_logger.info(\"Completed bronze to silver processing for customer_cleaned\")\n", "    return final_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Process customer data\n", "silver_df = process_bronze_to_silver(target_date)\n", "if silver_df is not None:\n", "    log_dataframe_info(silver_df, \"customer_silver_final\", customer_silver_logger)\n", "    display(silver_df)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": null, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5135065695670941, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "customer_silver", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}