{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Silver Layer Utility Functions\n", "\n", "This notebook contains utility functions for the silver layer processing, including:\n", "- Data type conversion\n", "- Column standardization\n", "- Data quality validation\n", "- Delta table operations (upsert, create external table)\n", "- Key validation\n", "\n", "These functions are designed to be reused across all silver layer notebooks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%run ../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["# Run the startup notebook to get access to common utilities and configurations\n", "from pyspark.sql.functions import (\n", "    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace,\n", "    to_timestamp, to_date, datediff, year, month, dayofmonth, dayofweek,\n", "    hour, minute, second, expr, concat, concat_ws, coalesce, regexp_extract,\n", "    isnan, isnull, length, substring\n", ")\n", "from pyspark.sql.types import (\n", "    StringType, IntegerType, LongType, FloatType, DoubleType, \n", "    BooleanType, TimestampType, DateType, DecimalType\n", ")\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "import re"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["# Initialize logger using log_path from config\n", "# component_log_levels is imported from start_up notebook\n", "silver_utils_logger = create_logger(component_log_levels=component_log_levels)\n", "silver_utils_logger.info(\"Initializing silver utilities\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Column Mapping Functions"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(silver_utils_logger)\n", "def apply_column_mapping(df, column_mapping):\n", "    \"\"\"Apply column mapping to rename columns from source to target names.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        column_mapping: Dictionary mapping source column names to target column names\n", "        \n", "    Returns:\n", "        DataFrame with renamed columns\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Applying column mapping: {column_mapping}\")\n", "    \n", "    # Create a new DataFrame with mapped column names\n", "    for source_col, target_col in column_mapping.items():\n", "        if source_col in df.columns:\n", "            df = df.withColumnRenamed(source_col, target_col)\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Type Conversion Functions"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(silver_utils_logger)\n", "def convert_data_types(df, type_mappings):\n", "    \"\"\"Convert columns to specified data types.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        type_mappings: Dictionary mapping column names to data types\n", "            Example: {\"customer_id\": StringType(), \"age\": IntegerType()}\n", "        \n", "    Returns:\n", "        DataFrame with converted data types\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Converting data types for columns: {list(type_mappings.keys())}\")\n", "    \n", "    for column, data_type in type_mappings.items():\n", "        if column in df.columns:\n", "            df = df.withColumn(column, col(column).cast(data_type))\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def convert_timestamp_columns(df, timestamp_columns, format_str=\"M/d/yyyy\"):\n", "    \"\"\"Convert string columns to timestamp type.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        timestamp_columns: List of column names to convert to timestamp\n", "        format_str: Date format string (default: \"M/d/yyyy\")\n", "        \n", "    Returns:\n", "        DataFrame with timestamp columns\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Converting timestamp columns: {timestamp_columns}\")\n", "    \n", "    for ts_col in timestamp_columns:\n", "        if ts_col in df.columns:\n", "            df = df.withColumn(ts_col, to_timestamp(col(ts_col), format_str))\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def convert_boolean_columns(df, boolean_columns):\n", "    \"\"\"Convert columns to boolean type.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        boolean_columns: List of column names to convert to boolean\n", "        \n", "    Returns:\n", "        DataFrame with boolean columns\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Converting boolean columns: {boolean_columns}\")\n", "    \n", "    for bool_col in boolean_columns:\n", "        if bool_col in df.columns:\n", "            # Handle various boolean representations\n", "            df = df.withColumn(\n", "                bool_col,\n", "                when(col(bool_col).isin([\"true\", \"True\", \"TRUE\", \"1\", \"t\", \"y\", \"yes\", \"Y\", \"Yes\"]), True)\n", "                .when(col(bool_col).isin([\"false\", \"False\", \"FALSE\", \"0\", \"f\", \"n\", \"no\", \"N\", \"No\"]), False)\n", "                .otherwise(None)  # Use NULL for values that don't match any pattern\n", "                .cast(BooleanType())\n", "            )\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def convert_numeric_columns(df, numeric_columns, data_type=DoubleType()):\n", "    \"\"\"Convert columns to numeric type.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        numeric_columns: List of column names to convert to numeric\n", "        data_type: Numeric data type to convert to (default: DoubleType)\n", "        \n", "    Returns:\n", "        DataFrame with numeric columns\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Converting numeric columns: {numeric_columns}\")\n", "    \n", "    for num_col in numeric_columns:\n", "        if num_col in df.columns:\n", "            # Clean numeric strings (remove currency symbols, commas, etc.)\n", "            df = df.withColumn(\n", "                num_col,\n", "                regexp_replace(col(num_col), \"[^-0-9.]\", \"\")\n", "            )\n", "            df = df.withColumn(num_col, col(num_col).cast(data_type))\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Column Standardization Functions"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(silver_utils_logger)\n", "def standardize_text_columns(df, text_columns, case=\"upper\"):\n", "    \"\"\"Standardize text columns by applying case conversion and trimming.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        text_columns: List of column names to standardize\n", "        case: Case conversion (\"upper\", \"lower\", or \"title\")\n", "        \n", "    Returns:\n", "        DataFrame with standardized text columns\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Standardizing text columns: {text_columns}\")\n", "    \n", "    for text_col in text_columns:\n", "        if text_col in df.columns:\n", "            # Trim whitespace\n", "            df = df.withColumn(text_col, trim(col(text_col)))\n", "            \n", "            # Apply case conversion\n", "            if case.lower() == \"upper\":\n", "                df = df.withColumn(text_col, upper(col(text_col)))\n", "            elif case.lower() == \"lower\":\n", "                df = df.withColumn(text_col, lower(col(text_col)))\n", "            elif case.lower() == \"title\":\n", "                # Simple title case implementation\n", "                df = df.withColumn(\n", "                    text_col,\n", "                    expr(f\"initcap({text_col})\")\n", "                )\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def standardize_phone_numbers(df, phone_columns):\n", "    \"\"\"Standardize phone number columns to format: XXX-XXX-XXXX.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        phone_columns: List of column names containing phone numbers\n", "        \n", "    Returns:\n", "        DataFrame with standardized phone numbers\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Standardizing phone columns: {phone_columns}\")\n", "    \n", "    for phone_col in phone_columns:\n", "        if phone_col in df.columns:\n", "            # Extract digits only\n", "            df = df.withColumn(\n", "                phone_col + \"_digits\",\n", "                regexp_replace(col(phone_col), \"[^0-9]\", \"\")\n", "            )\n", "            \n", "            # Format as XXX-XXX-XXXX (assuming 10-digit North American numbers)\n", "            df = df.withColumn(\n", "                phone_col,\n", "                when(\n", "                    length(col(phone_col + \"_digits\")) == 10,\n", "                    concat(\n", "                        substring(col(phone_col + \"_digits\"), 1, 3),\n", "                        lit(\"-\"),\n", "                        substring(col(phone_col + \"_digits\"), 4, 3),\n", "                        lit(\"-\"),\n", "                        substring(col(phone_col + \"_digits\"), 7, 4)\n", "                    )\n", "                ).otherwise(col(phone_col))\n", "            )\n", "            \n", "            # Drop temporary column\n", "            df = df.drop(phone_col + \"_digits\")\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def standardize_categorical_values(df, column, valid_values, default_value=None):\n", "    \"\"\"Standardize categorical values to ensure they are within a valid set.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        column: Column name to standardize\n", "        valid_values: List of valid values for the column\n", "        default_value: Default value to use for invalid values (default: None)\n", "        \n", "    Returns:\n", "        DataFrame with standardized categorical values\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Standardizing categorical column: {column}\")\n", "    \n", "    if column in df.columns:\n", "        # Convert to uppercase for case-insensitive comparison\n", "        df = df.withColumn(column, upper(col(column)))\n", "        \n", "        # Create validation column\n", "        df = df.withColumn(\n", "            column + \"_valid\",\n", "            col(column).isin([v.upper() for v in valid_values])\n", "        )\n", "        \n", "        # Replace invalid values with default if specified\n", "        if default_value is not None:\n", "            df = df.withColumn(\n", "                column,\n", "                when(col(column + \"_valid\"), col(column)).otherwise(lit(default_value))\n", "            )\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Quality Functions"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(silver_utils_logger)\n", "def validate_not_null(df, columns):\n", "    \"\"\"Validate that columns are not null.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        columns: List of column names to validate\n", "        \n", "    Returns:\n", "        DataFrame with validation columns added\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Validating not null columns: {columns}\")\n", "    \n", "    for col_name in columns:\n", "        if col_name in df.columns:\n", "            df = df.withColumn(\n", "                col_name + \"_not_null\",\n", "                ~(isnull(col(col_name)) | (col(col_name) == \"\"))\n", "            )\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def validate_unique(df, columns):\n", "    \"\"\"Validate that columns have unique values.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        columns: List of column names to validate\n", "        \n", "    Returns:\n", "        DataFrame with validation columns added\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Validating unique columns: {columns}\")\n", "    \n", "    for col_name in columns:\n", "        if col_name in df.columns:\n", "            # Count occurrences of each value\n", "            value_counts = df.groupBy(col_name).count()\n", "            \n", "            # Join back to original DataFrame\n", "            df = df.join(\n", "                value_counts.withColumnRenamed(\"count\", col_name + \"_count\"),\n", "                on=col_name,\n", "                how=\"left\"\n", "            )\n", "            \n", "            # Add validation column\n", "            df = df.withColumn(\n", "                col_name + \"_unique\",\n", "                col(col_name + \"_count\") == 1\n", "            )\n", "            \n", "            # Drop temporary column\n", "            df = df.drop(col_name + \"_count\")\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def validate_regex_pattern(df, column, pattern, validation_name=None):\n", "    \"\"\"Validate that column values match a regex pattern.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        column: Column name to validate\n", "        pattern: Regex pattern to match\n", "        validation_name: Name for the validation column (default: column + \"_valid\")\n", "        \n", "    Returns:\n", "        DataFrame with validation column added\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Validating regex pattern for column: {column}\")\n", "    \n", "    if column in df.columns:\n", "        validation_col = validation_name if validation_name else column + \"_valid\"\n", "        \n", "        df = df.withColumn(\n", "            validation_col,\n", "            col(column).rlike(pattern)\n", "        )\n", "    \n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def calculate_data_quality_score(df):\n", "    \"\"\"Calculate data quality score based on validation columns.\n", "    \n", "    Args:\n", "        df: Source DataFrame with validation columns (ending with _valid, _not_null, _unique)\n", "        \n", "    Returns:\n", "        DataFrame with data_quality_score column added\n", "    \"\"\"\n", "    silver_utils_logger.info(\"Calculating data quality score\")\n", "    \n", "    # Find all validation columns\n", "    validation_columns = [c for c in df.columns if c.endswith(\"_valid\") or c.endswith(\"_not_null\") or c.endswith(\"_unique\")]\n", "    \n", "    if validation_columns:\n", "        # Calculate quality score as percentage of passing validations\n", "        quality_expr = \" + \".join([f\"CASE WHEN {c} THEN 1 ELSE 0 END\" for c in validation_columns])\n", "        df = df.withColumn(\n", "            \"data_quality_score\",\n", "            expr(f\"({quality_expr}) * 100.0 / {len(validation_columns)}\")\n", "        )\n", "    else:\n", "        df = df.withColumn(\"data_quality_score\", lit(100.0))\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Delta Table Operations"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(silver_utils_logger)\n", "def create_external_table(spark, catalog, schema, table_name, location, format=\"delta\"):\n", "    \"\"\"Create an external table in Unity Catalog.\n", "    \n", "    Args:\n", "        spark: Spark session\n", "        catalog: Catalog name\n", "        schema: Schema name\n", "        table_name: Table name\n", "        location: Table location\n", "        format: Table format (default: \"delta\")\n", "        \n", "    Returns:\n", "        True if table was created, False if it already exists\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Creating external table {catalog}.{schema}.{table_name}\")\n", "    \n", "    try:\n", "        # Check if table exists\n", "        spark.sql(f\"DESCRIBE TABLE {catalog}.{schema}.{table_name}\")\n", "        silver_utils_logger.info(f\"Table {catalog}.{schema}.{table_name} already exists\")\n", "        return False\n", "    except AnalysisException:\n", "        # Create external table\n", "        spark.sql(f\"\"\"\n", "        CREATE EXTERNAL TABLE IF NOT EXISTS {catalog}.{schema}.{table_name}\n", "        USING {format}\n", "        LOCATION '{location}'\n", "        \"\"\")\n", "        silver_utils_logger.info(f\"Created external table {catalog}.{schema}.{table_name}\")\n", "        return True\n", "\n", "@log_execution(silver_utils_logger)\n", "def upsert_to_delta_table(spark, source_df, target_path, merge_condition, catalog=None, schema=None, table_name=None):\n", "    \"\"\"Upsert data to a Delta table using merge operation.\n", "    \n", "    Args:\n", "        spark: Spark session\n", "        source_df: Source DataFrame\n", "        target_path: Path to target Delta table\n", "        merge_condition: Merge condition (e.g., \"target.id = source.id\")\n", "        catalog: Optional catalog name\n", "        schema: Optional schema name\n", "        table_name: Optional table name\n", "        \n", "    Returns:\n", "        True if <PERSON><PERSON> was successful\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Upserting data to {target_path}\")\n", "    \n", "    try:\n", "        # Try to load the target Delta table\n", "        delta_table = DeltaTable.forPath(spark, target_path)\n", "        table_exists = True\n", "    except AnalysisException:\n", "        # Table doesn't exist, create it\n", "        table_exists = False\n", "    \n", "    if table_exists:\n", "        # Perform merge operation\n", "        silver_utils_logger.info(f\"Merging data with condition: {merge_condition}\")\n", "        delta_table.alias(\"target\").merge(\n", "            source_df.alias(\"source\"),\n", "            merge_condition\n", "        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()\n", "    else:\n", "        # Write data to new table\n", "        silver_utils_logger.info(f\"Creating new Delta table at {target_path}\")\n", "        if catalog and schema and table_name:\n", "            # Use fully qualified table name for Unity Catalog\n", "            full_table_name = f\"{catalog}.{schema}.{table_name}\"\n", "            silver_utils_logger.info(f\"Registering table in Unity Catalog as {full_table_name}\")\n", "            source_df.write.format(\"delta\").mode(\"overwrite\").option(\"path\", target_path).saveAsTable(full_table_name)\n", "        else:\n", "            # Just write to the path without registering in Unity Catalog\n", "            silver_utils_logger.info(f\"Writing to Delta path without Unity Catalog registration\")\n", "            source_df.write.format(\"delta\").mode(\"overwrite\").save(target_path)\n", "    \n", "    return True\n", "\n", "@log_execution(silver_utils_logger)\n", "def validate_primary_key(df, primary_key):\n", "    \"\"\"Validate that primary key column(s) have unique, non-null values.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        primary_key: Primary key column name or list of column names for composite key\n", "        \n", "    Returns:\n", "        Tuple of (DataFrame with validation columns, is_valid boolean)\n", "    \"\"\"\n", "    silver_utils_logger.info(f\"Validating primary key: {primary_key}\")\n", "    \n", "    # Convert single column to list\n", "    if isinstance(primary_key, str):\n", "        primary_key = [primary_key]\n", "    \n", "    # Validate not null\n", "    df = validate_not_null(df, primary_key)\n", "    \n", "    # Check for uniqueness of the key combination\n", "    key_counts = df.groupBy(*primary_key).count()\n", "    \n", "    # Join back to original DataFrame\n", "    df = df.join(\n", "        key_counts.withColumnRenamed(\"count\", \"pk_count\"),\n", "        on=primary_key,\n", "        how=\"left\"\n", "    )\n", "    \n", "    # Add validation column\n", "    pk_col_name = \"_\".join(primary_key) + \"_pk_valid\"\n", "    df = df.withColumn(pk_col_name, col(\"pk_count\") == 1)\n", "    \n", "    # Check if all primary key values are valid\n", "    pk_valid = df.filter(~col(pk_col_name)).count() == 0\n", "    \n", "    # Drop temporary column\n", "    df = df.drop(\"pk_count\")\n", "    \n", "    return df, pk_valid"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Read/Write Functions"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(silver_utils_logger)\n", "def read_bronze_data(spark, bronze_path, table_name, date=None, logger=None):\n", "    \"\"\"Read data from bronze layer.\n", "    \n", "    Args:\n", "        spark: Spark session\n", "        bronze_path: Path to bronze layer\n", "        table_name: Table name to read\n", "        date: Optional date partition to read (default: None, reads all partitions)\n", "        logger: Optional logger to use (default: silver_utils_logger)\n", "        \n", "    Returns:\n", "        DataFrame with bronze data\n", "    \"\"\"\n", "    log = logger if logger else silver_utils_logger\n", "    log.info(f\"Reading bronze data for table: {table_name}, date: {date if date else 'all'}\") \n", "    \n", "    if date:\n", "        # Read specific partition\n", "        df = spark.read.option(\"mergeSchema\", \"true\").parquet(f\"{bronze_path}/{table_name}/{date.replace('-', '/')}\")\n", "    else:\n", "        # Read all partitions with schema merging\n", "        df = spark.read.option(\"mergeSchema\", \"true\").parquet(f\"{bronze_path}/{table_name}\")\n", "    \n", "    if logger:\n", "        return log_dataframe_info(df, f\"{table_name}_bronze\", logger)\n", "    return df\n", "\n", "@log_execution(silver_utils_logger)\n", "def write_silver_data(spark, df, silver_path, table_name, primary_key, column_mapping=None, catalog=None, schema=None, format=\"delta\", logger=None):\n", "    \"\"\"Write data to silver layer using Delta format.\n", "    \n", "    Args:\n", "        spark: Spark session\n", "        df: DataFrame to write\n", "        silver_path: Path to silver layer\n", "        table_name: Table name to write\n", "        primary_key: Primary key column name or list of column names\n", "        column_mapping: Optional dictionary mapping source column names to target column names\n", "        catalog: Optional catalog name\n", "        schema: Optional schema name\n", "        format: Table format (default: \"delta\")\n", "        logger: Optional logger to use (default: silver_utils_logger)\n", "        \n", "    Returns:\n", "        DataFrame that was written\n", "    \"\"\"\n", "    log = logger if logger else silver_utils_logger\n", "    log.info(f\"Writing data to silver layer for table: {table_name}\") \n", "    \n", "    target_path = f\"{silver_path}/{table_name}\"\n", "    \n", "    # Apply column mapping if provided\n", "    if column_mapping:\n", "        log.info(f\"Applying column mapping before merge: {column_mapping}\")\n", "        df = apply_column_mapping(df, column_mapping)\n", "    \n", "    # Convert primary_key to string if it's a list\n", "    if isinstance(primary_key, list):\n", "        merge_condition = \" AND \".join([f\"target.{pk} = source.{pk}\" for pk in primary_key])\n", "    else:\n", "        merge_condition = f\"target.{primary_key} = source.{primary_key}\"\n", "    \n", "    # Use the upsert utility function\n", "    upsert_to_delta_table(\n", "        spark=spark,\n", "        source_df=df,\n", "        target_path=target_path,\n", "        merge_condition=merge_condition,\n", "        catalog=catalog,\n", "        schema=schema,\n", "        table_name=table_name\n", "    )\n", "    \n", "    if logger:\n", "        return log_dataframe_info(df, f\"{table_name}_silver\", logger)\n", "    return df"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}