# Knowledge Transfer (KT) Document  
## **Sobeys Data Centralization & Analytics Project**  

### **1. Project Overview**  
#### **Objective**  
Sobeys operates multiple banners with scattered data across various locations. This project aims to:  
- **Centralize data** to enable **cross-banner insights**.  
- **Standardize and clean** the data to improve consistency and usability.  
- Implement **Medallion Architecture in Databricks** for structured data processing.  
- Enable **Unity Catalog** for data governance, access control, and data discoverability.  
- Utilize **Azure Data Lake Storage (ADLS)** for scalable storage.  
- Set up **daily data ingestion pipelines** from ADLS into Databricks.  

#### **Business Goals**  
The project focuses on achieving:  
1. **Data Governance** – Enforce security, tracking, and access control.  
2. **Data Discoverability** – Ensure datasets can be easily located and analyzed.  
3. **Data Consumption** – Provide structured and cleaned data for reporting and analytics.  
4. **Access Control** – Manage permissions securely using Unity Catalog.  

---

### **2. Key Stakeholders**  
| Role | Responsibility |  
|------|--------------|  
| **Vice President of Advanced Analytics** | Business owner, provides vision & priorities |  
| **Data Engineers** | Develop ingestion, transformation pipelines & optimize architecture |  
| **Data Scientists** | Consume structured data for insights & reporting |  
| **Azure & Databricks Administrators** | Maintain infrastructure, storage, and security policies |  

---
### Scope
- **Data Sources**: The project will ingest multiple datasets including:
  - `customer`, `invoice`, `product`, `item`, `invoice_line`, `cost`, `site`, `discount`, `tax`.
- **Technology Stack**:
  - **Databricks** for processing & transformations.
  - **Azure Data Lake Storage (ADLS)** for raw data storage.
  - **Unity Catalog** for governance & security.
- **Business Goals**:
  1. **Data Governance** – Enforce security, tracking, and access policies.
  2. **Data Discoverability** – Ensure datasets can be easily located and analyzed.
  3. **Consumption** – Provide structured, cleaned data for reporting and analytics.
  4. **Access Control** – Manage permissions and security using Unity Catalog.

---

### **3. Technical Implementation**  
#### **Medallion Architecture**  
- **Bronze Layer** → Raw ingested data from ADLS  
- **Silver Layer** → Cleansed & validated data  
- **Gold Layer** → Business-ready analytics data  

#### **Storage & Processing**  
- **Storage**: ADLS Gen2 for raw, processed, and analytical data.  
- **Processing**: Databricks notebooks and jobs handle ingestion and transformations.  
- **Governance**: Unity Catalog for access control and RBAC (Role-Based Access Control).  

#### **Daily Ingestion Process**  
1. Extract data from ADLS (CSV format).  
2. Load data into Bronze Layer (Parquet) in Databricks.  
3. Apply cleaning and transformation rules, moving data to the Silver Layer (Delta).  
4. Aggregate and prepare user-stories tables in Gold Layer (Delta).  
5. Grant appropriate access to users via Unity Catalog.  

---

### **4. Security & Compliance Considerations**  
- **Data Encryption** at rest and in transit.  
- **RBAC Policies** to restrict unauthorized access.  
- **Audit Logging** via Unity Catalog for compliance tracking.  
- **Monitoring & Alerts** for pipeline failures and security threats.  

---

### **5. Risk Assessment & Mitigation Strategies**  
| Risk | Mitigation Strategy |  
|------|---------------------|  
| Data inconsistencies across banners | Implement data standardization rules |  
| Pipeline failures impacting analytics | Enable monitoring & automated alerts |  
| Cost overruns | Optimize storage & compute resources |  



---
# ADLS Naming Conventions
## Landing
- `customer`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `cost`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `discount`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `invoice`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `invoice_line`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `item`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `product`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `site`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `tax`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
                                             

## Raw (parquet)
- `customer`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `cost`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `discount`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `invoice`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `invoice_line`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `item`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `product`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `site`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`
- `tax`
   * `Year (Yyyy)`
      * `Month (mm)`
         * `Day (dd)`

## Silver (Delta)
- `customer_cleaned`
- `cost_cleaned`
- `discount_cleaned`
- `product_cleaned`
- `invoice_cleaned`
- `invoice_line_cleaned`
- `item_cleaned`
- `site_cleaned`
- `tax_cleaned`


## Gold (Delta)
- `product_category_performance_analysis`
- `store_performance_by_region_and_format`
- `perishable_product_management_and_waste_management`
- `pricing_margin_optimization`
- `recent_customer_activity`

---

## Raw Notebooks
- **Landing to Raw** 

## Silver Notebooks
- **cleaned_customer**
- **cleaned_cost**
- **cleaned_product**
- **cleaned_invoice**
- **cleaned_invoice_line**
- **cleaned_site**
- **cleaned_tax**
- **cleaned_discount**


## Gold Notebooks
- **product_category_performance_analysis**
- **store_performance_by_region_and_format**
- **perishable_product_management_and_waste_management**
- **pricing_margin_optimization**
- **recent_customer_activity**
---

### **6. Expected Outcomes & Benefits**  
1. **Unified data architecture**, enabling seamless analysis across banners.  
2. **Governed & secured environment** using Unity Catalog policies.  
3. **Efficient and scalable data ingestion** with ADLS integration.  
4. **Improved analytics & reporting**, enhancing business decision-making.  

---

### **7. Next Steps**  
✅ Deploy the **Medallion Architecture** across ADLS & Databricks.  
✅ Automate **daily ingestion pipelines** with monitoring.  
✅ Define **RBAC policies** using Unity Catalog.  
✅ Implement **audit logging & security measures**.  
✅ Conduct **Knowledge Transfer (KT) sessions** for future teams.  

---

### **Final Thoughts**  
This document provides a **detailed overview** of the **Sobeys Unity Catalog Project**.  
It enables **knowledge transfer**, ensuring smooth onboarding for new team members.  
By following this structure, any stakeholder can **quickly understand the implementation** and **business objectives**. 🚀  