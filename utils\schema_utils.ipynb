%run ../start_up

# Run the startup notebook to get access to common utilities and configurations
from pyspark.sql.types import (
    StructType, StructField, StringType, IntegerType, LongType, 
    FloatType, DoubleType, BooleanType, TimestampType, DateType, 
    DecimalType, ArrayType, MapType
)
from pyspark.sql.functions import col, lit, when
import json
import re

# Initialize logger using log_path from config
# component_log_levels is imported from start_up notebook
schema_utils_logger = create_logger(component_log_levels=component_log_levels)
schema_utils_logger.info("Initializing schema utilities")

@log_execution(schema_utils_logger)
def get_schema(table_name, layer):
    """Get the schema for a specific table and layer.
    
    Args:
        table_name: Name of the table
        layer: Layer name (bronze, silver, gold)
        
    Returns:
        StructType schema for the table
    """
    schema_utils_logger.info(f"Getting schema for {table_name} in {layer} layer")
    
    if table_name not in table_schemas or layer not in table_schemas[table_name]:
        schema_utils_logger.warning(f"No schema defined for {table_name} in {layer} layer")
        return None
    
    schema_dict = table_schemas[table_name][layer]
    schema_fields = [StructField(name, data_type, True) for name, data_type in schema_dict.items()]
    return StructType(schema_fields)

@log_execution(schema_utils_logger)
def apply_schema(df, schema):
    """Apply a schema to a DataFrame.
    
    Args:
        df: Source DataFrame
        schema: StructType schema to apply
        
    Returns:
        DataFrame with applied schema
    """
    schema_utils_logger.info("Applying schema to DataFrame")
    
    if schema is None:
        schema_utils_logger.warning("No schema provided, returning original DataFrame")
        return df
    
    # Apply schema by casting each column to the specified type
    for field in schema.fields:
        if field.name in df.columns:
            df = df.withColumn(field.name, col(field.name).cast(field.dataType))
    
    return df

@log_execution(schema_utils_logger)
def validate_schema(df, schema):
    """Validate that a DataFrame conforms to a schema.
    
    Args:
        df: Source DataFrame
        schema: StructType schema to validate against
        
    Returns:
        Tuple of (is_valid, validation_errors)
    """
    schema_utils_logger.info("Validating DataFrame against schema")
    
    if schema is None:
        schema_utils_logger.warning("No schema provided, validation skipped")
        return True, []
    
    validation_errors = []
    
    # Check for missing required columns
    schema_fields = {field.name for field in schema.fields}
    df_columns = set(df.columns)
    missing_columns = schema_fields - df_columns
    
    if missing_columns:
        error = f"Missing required columns: {missing_columns}"
        schema_utils_logger.warning(error)
        validation_errors.append(error)
    
    # Check data types
    for field in schema.fields:
        if field.name in df.columns:
            df_type = df.schema[field.name].dataType
            if str(df_type) != str(field.dataType):
                error = f"Column {field.name} has type {df_type}, expected {field.dataType}"
                schema_utils_logger.warning(error)
                validation_errors.append(error)
    
    is_valid = len(validation_errors) == 0
    return is_valid, validation_errors

@log_execution(schema_utils_logger)
def read_with_schema(spark, path, format, schema=None, options=None):
    """Read data with a specified schema.
    
    Args:
        spark: Spark session
        path: Path to the data
        format: Data format (csv, parquet, delta, etc.)
        schema: Optional schema to apply
        options: Optional dictionary of options
        
    Returns:
        DataFrame with the specified schema
    """
    schema_utils_logger.info(f"Reading data from {path} with format {format}")
    
    # Initialize reader
    reader = spark.read.format(format)
    
    # Apply options if provided
    if options:
        for key, value in options.items():
            reader = reader.option(key, value)
    
    # Apply schema if provided
    if schema:
        reader = reader.schema(schema)
        schema_utils_logger.info("Using explicit schema")
    else:
        schema_utils_logger.info("Using inferred schema")
    
    # Read the data
    df = reader.load(path)
    
    return df