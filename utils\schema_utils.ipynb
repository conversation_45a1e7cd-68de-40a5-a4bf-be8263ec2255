{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Schema Utility Functions\n", "\n", "This notebook contains utility functions for schema management, including:\n", "- Schema definition\n", "- Schema validation\n", "- Schema application\n", "\n", "These functions are designed to be reused across all layers of the medallion architecture."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%run ../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["# Run the startup notebook to get access to common utilities and configurations\n", "from pyspark.sql.types import (\n", "    StructType, StructField, StringType, IntegerType, LongType, \n", "    FloatType, DoubleType, BooleanType, TimestampType, DateType, \n", "    DecimalType, ArrayType, MapType\n", ")\n", "from pyspark.sql.functions import col, lit, when\n", "import json\n", "import re"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["# Initialize logger using log_path from config\n", "# component_log_levels is imported from start_up notebook\n", "schema_utils_logger = create_logger(component_log_levels=component_log_levels)\n", "schema_utils_logger.info(\"Initializing schema utilities\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["# Customer table schema definitions\n", "table_schemas = {\n", "    \"customer\": {\n", "        \"bronze\": {\n", "            \"cust_identifier\": StringType(),\n", "            \"name\": StringType(),\n", "            \"category\": StringType(),\n", "            \"mail\": StringType(),\n", "            \"telephone\": StringType(),\n", "            \"location\": StringType(),\n", "            \"enroll_date\": StringType(),\n", "            \"status\": StringType(),\n", "            \"level\": StringType(),\n", "            \"membership_num\": StringType(),\n", "            \"source_file_path\": StringType(),\n", "            \"ingestion_timestamp\": TimestampType(),\n", "            \"ingestion_date\": DateType()\n", "        },\n", "        \"silver\": {\n", "            \"customer_id\": StringType(),\n", "            \"customer_full_name\": StringType(),\n", "            \"customer_type\": StringType(),\n", "            \"customer_email_address\": StringType(),\n", "            \"phone_number\": StringType(),\n", "            \"address\": StringType(),\n", "            \"street_address\": StringType(),\n", "            \"city\": StringType(),\n", "            \"state_province\": StringType(),\n", "            \"postal_code\": StringType(),\n", "            \"registration_date\": TimestampType(),\n", "            \"is_active\": BooleanType(),\n", "            \"loyalty_tier\": StringType(),\n", "            \"loyalty_card_id\": StringType(),\n", "            \"quality_score_pct\": DoubleType(),\n", "            \"source_file_path\": StringType(),\n", "            \"ingestion_timestamp\": TimestampType(),\n", "            \"ingestion_date\": DateType()\n", "        },\n", "        \"gold\": {\n", "            \"sk_customer_id\": LongType(),\n", "            \"pk_customer_id\": StringType(),\n", "            \"customer_full_name\": StringType(),\n", "            \"customer_type\": StringType(),\n", "            \"customer_email_address\": StringType(),\n", "            \"phone_number\": StringType(),\n", "            \"address\": StringType(),\n", "            \"street_address\": StringType(),\n", "            \"city\": StringType(),\n", "            \"state_province\": StringType(),\n", "            \"postal_code\": StringType(),\n", "            \"registration_date\": TimestampType(),\n", "            \"is_active\": BooleanType(),\n", "            \"loyalty_card_id\": StringType(),\n", "            \"loyalty_tier\": StringType(),\n", "            \"total_lifetime_value\": DoubleType(),\n", "            \"total_order_count\": IntegerType(),\n", "            \"avg_order_value\": DoubleType(),\n", "            \"first_purchase_date\": TimestampType(),\n", "            \"last_purchase_date\": TimestampType(),\n", "            \"days_since_purchase\": IntegerType(),\n", "            \"customer_tenure_days\": IntegerType(),\n", "            \"avg_monthly_frequency\": DoubleType(),\n", "            \"customer_segment\": StringType(),\n", "            \"quality_score_pct\": DoubleType(),\n", "            \"created_at\": TimestampType(),\n", "            \"updated_at\": TimestampType(),\n", "            \"created_by\": StringType(),\n", "            \"modified_by\": StringType()\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(schema_utils_logger)\n", "def get_schema(table_name, layer):\n", "    \"\"\"Get the schema for a specific table and layer.\n", "    \n", "    Args:\n", "        table_name: Name of the table\n", "        layer: Layer name (bronze, silver, gold)\n", "        \n", "    Returns:\n", "        StructType schema for the table\n", "    \"\"\"\n", "    schema_utils_logger.info(f\"Getting schema for {table_name} in {layer} layer\")\n", "    \n", "    if table_name not in table_schemas or layer not in table_schemas[table_name]:\n", "        schema_utils_logger.warning(f\"No schema defined for {table_name} in {layer} layer\")\n", "        return None\n", "    \n", "    schema_dict = table_schemas[table_name][layer]\n", "    schema_fields = [Struct<PERSON>ield(name, data_type, True) for name, data_type in schema_dict.items()]\n", "    return StructType(schema_fields)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(schema_utils_logger)\n", "def apply_schema(df, schema):\n", "    \"\"\"Apply a schema to a DataFrame.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        schema: StructType schema to apply\n", "        \n", "    Returns:\n", "        DataFrame with applied schema\n", "    \"\"\"\n", "    schema_utils_logger.info(\"Applying schema to DataFrame\")\n", "    \n", "    if schema is None:\n", "        schema_utils_logger.warning(\"No schema provided, returning original DataFrame\")\n", "        return df\n", "    \n", "    # Apply schema by casting each column to the specified type\n", "    for field in schema.fields:\n", "        if field.name in df.columns:\n", "            df = df.withColumn(field.name, col(field.name).cast(field.dataType))\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(schema_utils_logger)\n", "def validate_schema(df, schema):\n", "    \"\"\"Validate that a DataFrame conforms to a schema.\n", "    \n", "    Args:\n", "        df: Source DataFrame\n", "        schema: StructType schema to validate against\n", "        \n", "    Returns:\n", "        Tuple of (is_valid, validation_errors)\n", "    \"\"\"\n", "    schema_utils_logger.info(\"Validating DataFrame against schema\")\n", "    \n", "    if schema is None:\n", "        schema_utils_logger.warning(\"No schema provided, validation skipped\")\n", "        return True, []\n", "    \n", "    validation_errors = []\n", "    \n", "    # Check for missing required columns\n", "    schema_fields = {field.name for field in schema.fields}\n", "    df_columns = set(df.columns)\n", "    missing_columns = schema_fields - df_columns\n", "    \n", "    if missing_columns:\n", "        error = f\"Missing required columns: {missing_columns}\"\n", "        schema_utils_logger.warning(error)\n", "        validation_errors.append(error)\n", "    \n", "    # Check data types\n", "    for field in schema.fields:\n", "        if field.name in df.columns:\n", "            df_type = df.schema[field.name].dataType\n", "            if str(df_type) != str(field.dataType):\n", "                error = f\"Column {field.name} has type {df_type}, expected {field.dataType}\"\n", "                schema_utils_logger.warning(error)\n", "                validation_errors.append(error)\n", "    \n", "    is_valid = len(validation_errors) == 0\n", "    return is_valid, validation_errors"]}, {"cell_type": "code", "execution_count": 0, "metadata": {}, "outputs": [], "source": ["@log_execution(schema_utils_logger)\n", "def read_with_schema(spark, path, format, schema=None, options=None):\n", "    \"\"\"Read data with a specified schema.\n", "    \n", "    Args:\n", "        spark: Spark session\n", "        path: Path to the data\n", "        format: Data format (csv, parquet, delta, etc.)\n", "        schema: Optional schema to apply\n", "        options: Optional dictionary of options\n", "        \n", "    Returns:\n", "        DataFrame with the specified schema\n", "    \"\"\"\n", "    schema_utils_logger.info(f\"Reading data from {path} with format {format}\")\n", "    \n", "    # Initialize reader\n", "    reader = spark.read.format(format)\n", "    \n", "    # Apply options if provided\n", "    if options:\n", "        for key, value in options.items():\n", "            reader = reader.option(key, value)\n", "    \n", "    # Apply schema if provided\n", "    if schema:\n", "        reader = reader.schema(schema)\n", "        schema_utils_logger.info(\"Using explicit schema\")\n", "    else:\n", "        schema_utils_logger.info(\"Using inferred schema\")\n", "    \n", "    # Read the data\n", "    df = reader.load(path)\n", "    \n", "    return df"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}