# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# Imports
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, to_date, date_format, year, month, dayofweek, weekofyear, quarter, date_add
from pyspark.sql.types import IntegerType, BooleanType, StringType, DateType
from datetime import datetime

# Initialize logger using configuration from startup
dim_date_logger = create_logger(component_log_levels=component_log_levels)
dim_date_logger.info("🚀 Initializing dim_date notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
gold_schema = pipeline_config["schemas"]["gold"]
storage_path = f"{pipeline_config['paths']['gold_path']}/dim_date"
gold_format = pipeline_config["file_formats"]["gold"]

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {gold_schema}")

dim_date_logger.info(f"Configuration initialized with catalog: {catalog}, schema: {gold_schema}, format: {gold_format}")

# COMMAND ----------
# Utility Functions
@log_execution(dim_date_logger)
def get_table_config(table_name, config):
    """Retrieve table configuration from provided config dictionary."""
    dim_date_logger.debug(f"Retrieving configuration for table: {table_name}")
    return config.get(table_name, {})

@log_execution(dim_date_logger)
def get_spark_types(type_str):
    """Map config types to Spark data types."""
    type_mapping = {
        "INT": IntegerType(),
        "STRING": StringType(),
        "BOOLEAN": BooleanType(),
        "DATE": DateType()
    }
    dim_date_logger.debug(f"Mapping type '{type_str}' to Spark type")
    return type_mapping.get(type_str.upper())

# COMMAND ----------
# Date Dimension Generator
@log_execution(dim_date_logger)
def generate_date_dim(spark, start_date, end_date):
    """Generate date dimension DataFrame."""
    dim_date_logger.log_start(f"generating date dimension from {start_date} to {end_date}", "transformation")

    try:
        # Validate date formats
        def is_valid_date(date_str):
            try:
                datetime.strptime(date_str, "%Y-%m-%d")
                return True
            except ValueError:
                return False

        if not is_valid_date(start_date):
            dim_date_logger.error(f"Invalid start_date format: {start_date}. Expected format: yyyy-MM-dd")
            raise ValueError(f"Invalid start_date format: {start_date}. Expected format: yyyy-MM-dd")
        if not is_valid_date(end_date):
            dim_date_logger.error(f"Invalid end_date format: {end_date}. Expected format: yyyy-MM-dd")
            raise ValueError(f"Invalid end_date format: {end_date}. Expected format: yyyy-MM-dd")

        dim_date_logger.debug(f"Validated start_date: {start_date}, end_date: {end_date}")

        # Create a DataFrame with the date range parameters
        dates_df = spark.createDataFrame([(start_date, end_date)], ["start_date", "end_date"])
        dates_df.createOrReplaceTempView("dates_temp")

        # Use the DataFrame columns in the SQL query to avoid string injection
        df = spark.sql(f"""
            WITH dates AS (
                SELECT
                    TO_DATE(start_date) AS start_date,
                    TO_DATE(end_date) AS end_date
                FROM dates_temp
            ),
            numbers AS (
                SELECT EXPLODE(SEQUENCE(0, DATEDIFF(end_date, start_date))) AS num
                FROM dates
            ),
            date_range AS (
                SELECT DATE_ADD((SELECT start_date FROM dates), num) AS full_date
                FROM numbers
            )
            SELECT
                CAST(DATE_FORMAT(full_date, 'yyyyMMdd') AS INT) AS date_id,
                DATE_FORMAT(full_date, 'EEEE') AS day_name,
                DATE_FORMAT(full_date, 'MMMM') AS month_name,
                CAST(
                    CASE
                        WHEN DAYOFWEEK(full_date) = 1 THEN 7
                        ELSE DAYOFWEEK(full_date) - 1
                    END AS INT
                ) AS day_of_week,
                CAST(WEEKOFYEAR(full_date) AS INT) AS week_of_year,
                CAST(MONTH(full_date) AS INT) AS month_number,
                CONCAT('Q', CAST(QUARTER(full_date) AS STRING)) AS quarter,
                CAST(YEAR(full_date) AS INT) AS calendar_year,
                CAST(
                    CASE
                        WHEN MONTH(full_date) >= 4 THEN YEAR(full_date) + 1
                        ELSE YEAR(full_date)
                    END AS INT
                ) AS fiscal_year,
                CAST(
                    CASE WHEN DAYOFWEEK(full_date) IN (1, 7) THEN TRUE ELSE FALSE END
                    AS BOOLEAN
                ) AS is_weekend,
                full_date
            FROM date_range
            ORDER BY date_id
        """)

        # Log DataFrame information
        log_dataframe_info(df, "date_dimension", dim_date_logger, "transformation")
        return df

    except Exception as e:
        dim_date_logger.error(f"Error generating date dimension: {str(e)}")
        raise

# COMMAND ----------
# Table Creation
@log_execution(dim_date_logger)
def create_table(spark, table_name, table_config, storage_path):
    """Create Delta table based on config."""
    dim_date_logger.log_start(f"creating table {table_name}", "data_writing")

    try:
        schema_def = ", ".join(
            f"{col['name']} {col['type']}{' NOT NULL' if not col.get('nullable', True) else ''}"
            for col in table_config["schema"]
        )
        primary_key = table_config.get("primary_key")
        partition_by = table_config.get("partition_by")

        dim_date_logger.info(f"Creating table with primary key: {primary_key}, partitioned by: {partition_by}")

        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {schema_def},
                CONSTRAINT pk_{primary_key} PRIMARY KEY ({primary_key})
            )
            USING DELTA
            PARTITIONED BY ({partition_by})
            LOCATION '{storage_path}'
        """

        dim_date_logger.debug(f"Executing CREATE TABLE SQL: {create_sql}")
        spark.sql(create_sql)

        dim_date_logger.info(f"Successfully created table {table_name}")

    except Exception as e:
        dim_date_logger.error(f"Error creating table {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function
@log_execution(dim_date_logger)
def process_dim_date(spark, catalog, schema, table_config, storage_path, start_date="2000-01-01"):
    """Main function to process dim_date table."""
    dim_date_logger.log_start(f"processing dim_date table", "transformation")

    table_name = f"{catalog}.{schema}.dim_date"
    dim_date_logger.info(f"Processing table: {table_name}")
    dim_date_logger.info(f"Storage path: {storage_path}")
    dim_date_logger.info(f"Start date: {start_date}")

    try:
        # Get current date and year
        dim_date_logger.debug("Getting current date and year")
        current_date_df = spark.sql("SELECT CURRENT_DATE() AS current_date")
        current_date = current_date_df.collect()[0]["current_date"]
        current_year = spark.sql("SELECT YEAR(CURRENT_DATE()) AS current_year").collect()[0]["current_year"]

        dim_date_logger.info(f"Current date: {current_date}, Current year: {current_year}")

        # Check if table exists
        dim_date_logger.debug(f"Checking if table {table_name} exists")
        table_exists = spark.catalog.tableExists(table_name)
        dim_date_logger.info(f"Table exists: {table_exists}")

        if not table_exists:
            # Create table and populate with initial data
            dim_date_logger.info("Table does not exist, creating new table with initial data")
            end_date = f"{current_year}-12-31"
            dim_date_logger.info(f"Generating initial data from {start_date} to {end_date}")

            df = generate_date_dim(spark, start_date, end_date)
            create_table(spark, table_name, table_config, storage_path)

            dim_date_logger.log_start(f"writing initial data to {table_name}", "data_writing")
            df.write.format("delta").partitionBy(table_config["partition_by"]).mode("append").option("path", storage_path).saveAsTable(table_name)
            dim_date_logger.info(f"Created table {table_name} with data from {start_date} to {end_date}")

        else:
            # Check if current date exists
            dim_date_logger.debug("Table exists, checking if current date data exists")
            current_date_exists = spark.sql(f"""
                SELECT COUNT(*) AS cnt
                FROM {table_name}
                WHERE full_date = CURRENT_DATE()
            """).collect()[0]["cnt"] > 0

            dim_date_logger.info(f"Current date exists in table: {current_date_exists}")

            if not current_date_exists:
                # Append data for current year
                dim_date_logger.info("Current date not found, appending data for current year")
                start_of_year = f"{current_year}-01-01"
                end_of_year = f"{current_year}-12-31"
                dim_date_logger.info(f"Generating data for year {current_year} from {start_of_year} to {end_of_year}")

                df = generate_date_dim(spark, start_of_year, end_of_year)

                dim_date_logger.log_start(f"appending year {current_year} data to {table_name}", "data_writing")
                df.write.format("delta").partitionBy(table_config["partition_by"]).mode("append").option("path", storage_path).saveAsTable(table_name)
                dim_date_logger.info(f"Appended data for year {current_year} to {table_name}")

            else:
                dim_date_logger.info(f"Current date {current_date} already exists in {table_name}. No action needed.")

        # Optimize table
        dim_date_logger.log_start(f"optimizing table {table_name}", "data_writing")
        spark.sql(f"OPTIMIZE {table_name}")
        dim_date_logger.info(f"Successfully optimized table {table_name}")
        dim_date_logger.log_end(f"optimizing table {table_name}", "data_writing")

        dim_date_logger.log_end(f"processing dim_date table", "transformation")

    except Exception as e:
        dim_date_logger.error(f"Error processing dim_date table: {str(e)}")
        raise

# COMMAND ----------
# Main execution
dim_date_logger.info("Starting dim_date processing")

# Process dim_date table using configuration
try:
    process_dim_date(spark, catalog, gold_schema, table_config["dim_date"], storage_path)
    dim_date_logger.info("✅ Completed dim_date processing successfully")
except Exception as e:
    dim_date_logger.error(f"❌ Failed to process dim_date: {str(e)}")
    raise