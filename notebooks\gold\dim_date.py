# Databricks notebook source
# Imports
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, to_date, date_format, year, month, dayofweek, weekofyear, quarter, date_add
from pyspark.sql.types import IntegerType, BooleanType, StringType, DateType

# COMMAND ----------
# Utility Functions
def get_table_config(table_name, config):
    """Retrieve table configuration from provided config dictionary."""
    return config.get(table_name, {})

def get_spark_types(type_str):
    """Map config types to Spark data types."""
    type_mapping = {
        "INT": IntegerType(),
        "STRING": StringType(),
        "BOOLEAN": BooleanType(),
        "DATE": DateType()
    }
    return type_mapping.get(type_str.upper())

# COMMAND ----------
# Date Dimension Generator
def generate_date_dim(spark, start_date, end_date):
    """Generate date dimension DataFrame."""
    return spark.sql(f"""
        WITH dates AS (
            SELECT 
                TO_DATE('{start_date}') AS start_date,
                TO_DATE('{end_date}') AS end_date
        ),
        numbers AS (
            SELECT EXPLODE(SEQUENCE(0, DATEDIFF(end_date, start_date))) AS num
            FROM dates
        ),
        date_range AS (
            SELECT DATE_ADD((SELECT start_date FROM dates), num) AS full_date
            FROM numbers
        )
        SELECT
            CAST(DATE_FORMAT(full_date, 'yyyyMMdd') AS INT) AS date_id,
            DATE_FORMAT(full_date, 'EEEE') AS day_name,
            DATE_FORMAT(full_date, 'MMMM') AS month_name,
            CAST(
                CASE 
                    WHEN DAYOFWEEK(full_date) = 1 THEN 7
                    ELSE DAYOFWEEK(full_date) - 1
                END AS INT
            ) AS day_of_week,
            CAST(WEEKOFYEAR(full_date) AS INT) AS week_of_year,
            CAST(MONTH(full_date) AS INT) AS month_number,
            CONCAT('Q', CAST(QUARTER(full_date) AS STRING)) AS quarter,
            CAST(YEAR(full_date) AS INT) AS calendar_year,
            CAST(
                CASE 
                    WHEN MONTH(full_date) >= 4 THEN YEAR(full_date) + 1
                    ELSE YEAR(full_date)
                END AS INT
            ) AS fiscal_year,
            CAST(
                CASE WHEN DAYOFWEEK(full_date) IN (1, 7) THEN TRUE ELSE FALSE END 
                AS BOOLEAN
            ) AS is_weekend,
            full_date
        FROM date_range
        ORDER BY date_id
    """)

# COMMAND ----------
# Table Creation
def create_table(spark, table_name, table_config, storage_path):
    """Create Delta table based on config."""
    schema_def = ", ".join(
        f"{col['name']} {col['type']}{' NOT NULL' if not col.get('nullable', True) else ''}"
        for col in table_config["schema"]
    )
    primary_key = table_config.get("primary_key")
    partition_by = table_config.get("partition_by")
    
    create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {schema_def},
            CONSTRAINT pk_{primary_key} PRIMARY KEY ({primary_key})
        )
        USING DELTA
        PARTITIONED BY ({partition_by})
        LOCATION '{storage_path}'
    """
    spark.sql(create_sql)

# COMMAND ----------
# Main Processing Function
def process_dim_date(spark, catalog, schema, table_config, storage_path, start_date="2000-01-01"):
    """Main function to process dim_date table."""
    table_name = f"{catalog}.{schema}.dim_date"
    
    # Get current date and year
    current_date_df = spark.sql("SELECT CURRENT_DATE() AS current_date")
    current_date = current_date_df.collect()[0]["current_date"]
    current_year = spark.sql("SELECT YEAR(CURRENT_DATE()) AS current_year").collect()[0]["current_year"]
    
    # Check if table exists
    table_exists = spark.catalog.tableExists(table_name)
    
    if not table_exists:
        # Create table and populate with initial data
        end_date = f"{current_year}-12-31"
        df = generate_date_dim(spark, start_date, end_date)
        create_table(spark, table_name, table_config, storage_path)
        df.write.format("delta").partitionBy(table_config["partition_by"]).mode("append").option("path", storage_path).saveAsTable(table_name)
        print(f"Created table {table_name} with data from {start_date} to {end_date}")
    else:
        # Check if current date exists
        current_date_exists = spark.sql(f"""
            SELECT COUNT(*) AS cnt
            FROM {table_name}
            WHERE full_date = CURRENT_DATE()
        """).collect()[0]["cnt"] > 0
        
        if not current_date_exists:
            # Append data for current year
            start_of_year = f"{current_year}-01-01"
            end_of_year = f"{current_year}-12-31"
            df = generate_date_dim(spark, start_of_year, end_of_year)
            df.write.format("delta").partitionBy(table_config["partition_by"]).mode("append").option("path", storage_path).saveAsTable(table_name)
            print(f"Appended data for year {current_year} to {table_name}")
        else:
            print(f"Current date {current_date} already exists in {table_name}. No action needed.")
    
    # Optimize table
    spark.sql(f"OPTIMIZE {table_name}")

# COMMAND ----------
# Example Usage
catalog = "sobeys_uc"
gold_schema = "gold"
storage_path = "abfss://<EMAIL>/catalog_sobeys_uc/gold/dim_date"
table_config = {
    "dim_date": {
        "schema": [
            {"name": "date_id", "type": "INT", "nullable": False},
            {"name": "day_name", "type": "STRING"},
            {"name": "month_name", "type": "STRING"},
            {"name": "day_of_week", "type": "INT"},
            {"name": "week_of_year", "type": "INT"},
            {"name": "month_number", "type": "INT"},
            {"name": "quarter", "type": "STRING"},
            {"name": "calendar_year", "type": "INT"},
            {"name": "fiscal_year", "type": "INT"},
            {"name": "is_weekend", "type": "BOOLEAN"},
            {"name": "full_date", "type": "DATE"},
        ],
        "primary_key": "date_id",
        "partition_by": "calendar_year",
    }}
process_dim_date(spark, catalog, gold_schema, table_config["dim_date"], storage_path)